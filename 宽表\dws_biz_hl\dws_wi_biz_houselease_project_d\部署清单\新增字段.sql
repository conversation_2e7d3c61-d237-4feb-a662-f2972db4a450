ALTER TABLE dwd.dwd_ast_lease_deal_rec_fct ADD COLUMNS (
    cntr_eff_dt STRING COMMENT '合同生效日期'
) CASCADE;

ALTER TABLE dwd.dwd_ast_lease_deal_rec_fct ADD COLUMNS (
    is_mm_handle_changed STRING COMMENT '保证金处置方式是否有变更' -- BZJCZFSSFYBG
) CASCADE;


ALTER TABLE dwd.dwd_ast_lease_deal_rec_fct ADD COLUMNS (
    is_rent_cover_other_fees STRING COMMENT '租金挂牌价是否含其他费用' -- ZJGPJSFBHQTFY
) CASCADE;


-- 部署生产的时候需要确认业务环境有这个字段
-- ALTER TABLE std.std_bjhl_tzccz_czfxx_d ADD COLUMNS (
--     ast_src_entp_5_cla STRING COMMENT '资产来源（企业5级）'
-- ) CASCADE;


ALTER TABLE std.std_bjhl_tzccz_zcczxm_d ADD COLUMNS (
    is_operate STRING COMMENT '是否运营房源信息' -- SFYYFYXX
) CASCADE;


ALTER TABLE std.std_bjhl_tzccz_cjjl_d ADD COLUMNS (
    is_mm_handle_changed STRING COMMENT '保证金处置方式是否有变更' -- BZJCZFSSFYBG
) CASCADE;


ALTER TABLE std.std_bjhl_tzccz_zcczxm_d ADD COLUMNS (
    is_rent_cover_other_fees STRING COMMENT '租金挂牌价是否含其他费用' -- ZJGPJSFBHQTFY
) CASCADE;

ALTER TABLE dwd.dwd_ast_lease_deal_rec_fct ADD COLUMNS (
    mdl_sts decimal(12,0) COMMENT '成交状态'
) CASCADE;


-- 删除前备份

CREATE TABLE `ods.ods_bjhl_tzccz_czfxx`(
  `id` decimal(16,0) COMMENT 'ID', 
  `khh` string COMMENT 'KHH', 
  `czflx` string COMMENT 'CZFLX', 
  `xmlx` decimal(12,0) COMMENT 'XMLX', 
  `czfmc` string COMMENT 'CZFMC', 
  `province` string COMMENT 'PROVINCE', 
  `city` string COMMENT 'CITY', 
  `qx` string COMMENT 'QX', 
  `zcd` string COMMENT 'ZCD', 
  `fddbr` string COMMENT 'FDDBR', 
  `zczb` decimal(16,6) COMMENT 'ZCZB', 
  `zczbbz` string COMMENT 'ZCZBBZ', 
  `sshylx` string COMMENT 'SSHYLX', 
  `sshy` string COMMENT 'SSHY', 
  `jjlx` string COMMENT 'JJLX', 
  `czqy_tyshxydm` string COMMENT 'CZQY_TYSHXYDM', 
  `gzjgjg` string COMMENT 'GZJGJG', 
  `jgdqs` string COMMENT 'JGDQS', 
  `jgdqshi` string COMMENT 'JGDQSHI', 
  `czqy` string COMMENT 'CZQY', 
  `ssjt` string COMMENT 'SSJT', 
  `zjlx` decimal(16,0) COMMENT 'ZJLX', 
  `zjhm` string COMMENT 'ZJHM', 
  `lxr` string COMMENT 'LXR', 
  `lxdh` string COMMENT 'LXDH', 
  `dzyj` string COMMENT 'DZYJ', 
  `txdz` string COMMENT 'TXDZ', 
  `yhzhmc` string COMMENT 'YHZHMC', 
  `yhzhzh` string COMMENT 'YHZHZH', 
  `khyh` string COMMENT 'KHYH', 
  `khyhzh` string COMMENT 'KHYHZH', 
  `khhlhh` string COMMENT 'KHHLHH', 
  `bz` string COMMENT 'BZ', 
  `xmid` decimal(16,0) COMMENT 'XMID', 
  `xmid_ypl` decimal(16,0) COMMENT 'XMID_YPL', 
  `zcly2` string COMMENT 'ZCLY2', 
  `zcly3` string COMMENT 'ZCLY3', 
  `zcly4` string COMMENT 'ZCLY4', 
  `zcly5` string COMMENT 'ZCLY5', 
  `dwxz` decimal(12,0) COMMENT 'DWXZ', 
  `zzsxrq` timestamp COMMENT 'ZZSXRQ', 
  `ysdm` string COMMENT 'YSDM', 
  `lxr2` string COMMENT 'LXR2', 
  `lxdh2` string COMMENT 'LXDH2', 
  `fkfx` decimal(12,0) COMMENT 'FKFX', 
  `jfly` string COMMENT 'JFLY', 
  `fmdw` string COMMENT 'FMDW', 
  `czfxxsfpl` decimal(12,0) COMMENT 'CZFXXSFPL', 
  `qycj` decimal(12,0) COMMENT 'QYCJ', 
  `fkhh` string COMMENT 'FKHH', 
  `fid` decimal(16,0) COMMENT 'FID', 
  `grade` decimal(16,0) COMMENT 'GRADE', 
  `fdncode` string COMMENT 'FDNCODE', 
  `zzsfcq` decimal(12,0) COMMENT 'ZZSFCQ', 
  `ztzgzmwj` string COMMENT 'ZTZGZMWJ', 
  `lxrsfz` string COMMENT 'LXRSFZ', 
  `dwmc` string COMMENT 'DWMC', 
  `sh` string COMMENT 'SH', 
  `dwdz` string COMMENT 'DWDZ', 
  `dhhm` string COMMENT 'DHHM', 
  `kpxx_khyh` string COMMENT 'KPXX_KHYH', 
  `kpxx_yhzh` string COMMENT 'KPXX_YHZH', 
  `zcly_yq1` decimal(16,0) COMMENT 'ZCLY_YQ1', 
  `zcly_yq2` decimal(16,0) COMMENT 'ZCLY_YQ2', 
  `zcly_yq3` decimal(16,0) COMMENT 'ZCLY_YQ3', 
  `zcly_yq4` decimal(16,0) COMMENT 'ZCLY_YQ4', 
  `wljjjgtzs` string COMMENT 'WLJJJGTZS', 
  `wljjjgtzs_ydq` string COMMENT 'WLJJJGTZS_YDQ', 
  `xgfj` string COMMENT 'XGFJ', 
  `sfgyfwcz` decimal(12,0) COMMENT 'SFGYFWCZ', 
  `wts` string COMMENT 'WTS', 
  `czfbj` decimal(12,0) COMMENT 'CZFBJ', 
  `dh` string COMMENT 'DH', 
  `sshymc` string COMMENT 'SSHYMC')
PARTITIONED BY ( 
  `dt` string)
ROW FORMAT SERDE 
  'org.apache.hadoop.hive.ql.io.orc.OrcSerde' 
WITH SERDEPROPERTIES ( 
  'colelction.delim'='', 
  'field.delim'=',', 
  'mapkey.delim'='', 
  'serialization.format'=',') 
STORED AS INPUTFORMAT 
  'org.apache.hadoop.hive.ql.io.orc.OrcInputFormat' 
OUTPUTFORMAT 
  'org.apache.hadoop.hive.ql.io.orc.OrcOutputFormat'
LOCATION
  'hdfs://cbex/user/hive/warehouse/ods.db/ods_bjhl_tzccz_czfxx'
TBLPROPERTIES (
  'orc.compress'='SNAPPY', 
  'spark.sql.create.version'='2.2 or prior', 
  'spark.sql.sources.schema.numPartCols'='1', 
  'spark.sql.sources.schema.numParts'='2', 
  'spark.sql.sources.schema.part.0'='{"type":"struct","fields":[{"name":"id","type":"decimal(16,0)","nullable":true,"metadata":{"comment":"ID"}},{"name":"khh","type":"string","nullable":true,"metadata":{"comment":"KHH"}},{"name":"czflx","type":"string","nullable":true,"metadata":{"comment":"CZFLX"}},{"name":"xmlx","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"XMLX"}},{"name":"czfmc","type":"string","nullable":true,"metadata":{"comment":"CZFMC"}},{"name":"province","type":"string","nullable":true,"metadata":{"comment":"PROVINCE"}},{"name":"city","type":"string","nullable":true,"metadata":{"comment":"CITY"}},{"name":"qx","type":"string","nullable":true,"metadata":{"comment":"QX"}},{"name":"zcd","type":"string","nullable":true,"metadata":{"comment":"ZCD"}},{"name":"fddbr","type":"string","nullable":true,"metadata":{"comment":"FDDBR"}},{"name":"zczb","type":"decimal(16,6)","nullable":true,"metadata":{"comment":"ZCZB"}},{"name":"zczbbz","type":"string","nullable":true,"metadata":{"comment":"ZCZBBZ"}},{"name":"sshylx","type":"string","nullable":true,"metadata":{"comment":"SSHYLX"}},{"name":"sshy","type":"string","nullable":true,"metadata":{"comment":"SSHY"}},{"name":"jjlx","type":"string","nullable":true,"metadata":{"comment":"JJLX"}},{"name":"czqy_tyshxydm","type":"string","nullable":true,"metadata":{"comment":"CZQY_TYSHXYDM"}},{"name":"gzjgjg","type":"string","nullable":true,"metadata":{"comment":"GZJGJG"}},{"name":"jgdqs","type":"string","nullable":true,"metadata":{"comment":"JGDQS"}},{"name":"jgdqshi","type":"string","nullable":true,"metadata":{"comment":"JGDQSHI"}},{"name":"czqy","type":"string","nullable":true,"metadata":{"comment":"CZQY"}},{"name":"ssjt","type":"string","nullable":true,"metadata":{"comment":"SSJT"}},{"name":"zjlx","type":"decimal(16,0)","nullable":true,"metadata":{"comment":"ZJLX"}},{"name":"zjhm","type":"string","nullable":true,"metadata":{"comment":"ZJHM"}},{"name":"lxr","type":"string","nullable":true,"metadata":{"comment":"LXR"}},{"name":"lxdh","type":"string","nullable":true,"metadata":{"comment":"LXDH"}},{"name":"dzyj","type":"string","nullable":true,"metadata":{"comment":"DZYJ"}},{"name":"txdz","type":"string","nullable":true,"metadata":{"comment":"TXDZ"}},{"name":"yhzhmc","type":"string","nullable":true,"metadata":{"comment":"YHZHMC"}},{"name":"yhzhzh","type":"string","nullable":true,"metadata":{"comment":"YHZHZH"}},{"name":"khyh","type":"string","nullable":true,"metadata":{"comment":"KHYH"}},{"name":"khyhzh","type":"string","nullable":true,"metadata":{"comment":"KHYHZH"}},{"name":"khhlhh","type":"string","nullable":true,"metadata":{"comment":"KHHLHH"}},{"name":"bz","type":"string","nullable":true,"metadata":{"comment":"BZ"}},{"name":"xmid","type":"decimal(16,0)","nullable":true,"metadata":{"comment":"XMID"}},{"name":"xmid_ypl","type":"decimal(16,0)","nullable":true,"metadata":{"comment":"XMID_YPL"}},{"name":"zcly2","type":"string","nullable":true,"metadata":{"comment":"ZCLY2"}},{"name":"zcly3","type":"string","nullable":true,"metadata":{"comment":"ZCLY3"}},{"name":"zcly4","type":"string","nullable":true,"metadata":{"comment":"ZCLY4"}},{"name":"zcly5","type":"string","nullable":true,"metadata":{"comment":"ZCLY5"}},{"name":"dwxz","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"DWXZ"}},{"name":"zzsxrq","type":"timestamp","nullable":true,"metadata":{"comment":"ZZSXRQ"}},{"name":"ysdm","type":"string","nullable":true,"metadata":{"comment":"YSDM"}},{"name":"lxr2","type":"string","nullable":true,"metadata":{"comment":"LXR2"}},{"name":"lxdh2","type":"string","nullable":true,"metadata":{"comment":"LXDH2"}},{"name":"fkfx","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"FKFX"}},{"name":"jfly","type":"string","nullable":true,"metadata":{"comment":"JFLY"}},{"name":"fmdw","type":"string","nullable":true,"metadata":{"comment":"FMDW"}},{"name":"czfxxsfpl","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"CZFXXSFPL"}},{"name":"qycj","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"QYCJ', 
  'spark.sql.sources.schema.part.1'='"}},{"name":"fkhh","type":"string","nullable":true,"metadata":{"comment":"FKHH"}},{"name":"fid","type":"decimal(16,0)","nullable":true,"metadata":{"comment":"FID"}},{"name":"grade","type":"decimal(16,0)","nullable":true,"metadata":{"comment":"GRADE"}},{"name":"fdncode","type":"string","nullable":true,"metadata":{"comment":"FDNCODE"}},{"name":"zzsfcq","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"ZZSFCQ"}},{"name":"ztzgzmwj","type":"string","nullable":true,"metadata":{"comment":"ZTZGZMWJ"}},{"name":"lxrsfz","type":"string","nullable":true,"metadata":{"comment":"LXRSFZ"}},{"name":"dwmc","type":"string","nullable":true,"metadata":{"comment":"DWMC"}},{"name":"sh","type":"string","nullable":true,"metadata":{"comment":"SH"}},{"name":"dwdz","type":"string","nullable":true,"metadata":{"comment":"DWDZ"}},{"name":"dhhm","type":"string","nullable":true,"metadata":{"comment":"DHHM"}},{"name":"kpxx_khyh","type":"string","nullable":true,"metadata":{"comment":"KPXX_KHYH"}},{"name":"kpxx_yhzh","type":"string","nullable":true,"metadata":{"comment":"KPXX_YHZH"}},{"name":"zcly_yq1","type":"decimal(16,0)","nullable":true,"metadata":{"comment":"ZCLY_YQ1"}},{"name":"zcly_yq2","type":"decimal(16,0)","nullable":true,"metadata":{"comment":"ZCLY_YQ2"}},{"name":"zcly_yq3","type":"decimal(16,0)","nullable":true,"metadata":{"comment":"ZCLY_YQ3"}},{"name":"zcly_yq4","type":"decimal(16,0)","nullable":true,"metadata":{"comment":"ZCLY_YQ4"}},{"name":"wljjjgtzs","type":"string","nullable":true,"metadata":{"comment":"WLJJJGTZS"}},{"name":"wljjjgtzs_ydq","type":"string","nullable":true,"metadata":{"comment":"WLJJJGTZS_YDQ"}},{"name":"xgfj","type":"string","nullable":true,"metadata":{"comment":"XGFJ"}},{"name":"sfgyfwcz","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"SFGYFWCZ"}},{"name":"wts","type":"string","nullable":true,"metadata":{"comment":"WTS"}},{"name":"czfbj","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"CZFBJ"}},{"name":"dh","type":"string","nullable":true,"metadata":{"comment":"DH"}},{"name":"sshymc","type":"string","nullable":true,"metadata":{"comment":"SSHYMC"}},{"name":"dt","type":"string","nullable":true,"metadata":{}}]}', 
  'spark.sql.sources.schema.partCol.0'='dt', 
  'transient_lastDdlTime'='1724935434')



  CREATE TABLE `ods.ods_bjhl_tzccz_zcczxm`(
  `id` decimal(16,0) COMMENT 'ID', 
  `jbr` decimal(12,0) COMMENT 'JBR', 
  `xmfzr` decimal(12,0) COMMENT 'XMFZR', 
  `xmlxr` string COMMENT 'XMLXR', 
  `xmlxrdh` string COMMENT 'XMLXRDH', 
  `bmfzr` decimal(12,0) COMMENT 'BMFZR', 
  `xmssbm` decimal(12,0) COMMENT 'XMSSBM', 
  `jjr` decimal(12,0) COMMENT 'JJR', 
  `hyjg` decimal(12,0) COMMENT 'HYJG', 
  `hylxr` string COMMENT 'HYLXR', 
  `hylxdh` string COMMENT 'HYLXDH', 
  `sfcxgp` decimal(12,0) COMMENT 'SFCXGP', 
  `glypl` decimal(16,0) COMMENT 'GLYPL', 
  `cjr` decimal(12,0) COMMENT 'CJR', 
  `cjsj` timestamp COMMENT 'CJSJ', 
  `xgr` decimal(12,0) COMMENT 'XGR', 
  `xgsj` timestamp COMMENT 'XGSJ', 
  `xmid` decimal(16,0) COMMENT 'XMID', 
  `xzjyfs` decimal(12,0) COMMENT 'XZJYFS', 
  `xzjyfsrq` timestamp COMMENT 'XZJYFSRQ', 
  `jyfsbgyy` string COMMENT 'JYFSBGYY', 
  `cjrq` string COMMENT 'CJRQ', 
  `cjjg` string COMMENT 'CJJG', 
  `sjjyfs` decimal(12,0) COMMENT 'SJJYFS', 
  `zzrq` decimal(8,0) COMMENT 'ZZRQ', 
  `zjrq` decimal(8,0) COMMENT 'ZJRQ', 
  `sjpljssj` timestamp COMMENT 'SJPLJSSJ', 
  `zcplrq` decimal(8,0) COMMENT 'ZCPLRQ', 
  `xmlx` decimal(12,0) COMMENT 'XMLX', 
  `xmcjrs` decimal(10,0) COMMENT 'XMCJRS', 
  `xmmc` string COMMENT 'XMMC', 
  `xmbh` string COMMENT 'XMBH', 
  `bdmc` string COMMENT 'BDMC', 
  `xmzt` decimal(9,0) COMMENT 'XMZT', 
  `gyzczr` decimal(12,0) COMMENT 'GYZCZR', 
  `zclb` decimal(38,0) COMMENT 'ZCLB', 
  `nbjcqk` decimal(12,0) COMMENT 'NBJCQK', 
  `nbjcqkqtsm` string COMMENT 'NBJCQKQTSM', 
  `pzdw` string COMMENT 'PZDW', 
  `pzwh` string COMMENT 'PZWH', 
  `gjsfwwpl` decimal(12,0) COMMENT 'GJSFWWPL', 
  `gjdwmc` string COMMENT 'GJDWMC', 
  `sffbgj` decimal(12,0) COMMENT 'SFFBGJ', 
  `zjgj` decimal(16,2) COMMENT 'ZJGJ', 
  `gjdw` decimal(12,0) COMMENT 'GJDW', 
  `qtgjdw` string COMMENT 'QTGJDW', 
  `yxczq` decimal(12,0) COMMENT 'YXCZQ', 
  `qtql` decimal(12,0) COMMENT 'QTQL', 
  `qtqlsm` string COMMENT 'QTQLSM', 
  `qtplsx` string COMMENT 'QTPLSX', 
  `xmfj` string COMMENT 'XMFJ', 
  `xmtp` string COMMENT 'XMTP', 
  `czfmc` string COMMENT 'CZFMC', 
  `sffbjj` decimal(12,0) COMMENT 'SFFBJJ', 
  `czdj` decimal(16,4) COMMENT 'CZDJ', 
  `czdjdw` decimal(12,0) COMMENT 'CZDJDW', 
  `qtdw` string COMMENT 'QTDW', 
  `zjgpjbz` string COMMENT 'ZJGPJBZ', 
  `nzjczflx` decimal(12,0) COMMENT 'NZJCZFLX', 
  `nzjczfgszs` decimal(10,0) COMMENT 'NZJCZFGSZS', 
  `nzjczfgszd` decimal(10,0) COMMENT 'NZJCZFGSZD', 
  `nzjczfgs` decimal(10,0) COMMENT 'NZJCZFGS', 
  `zlqlx` decimal(12,0) COMMENT 'ZLQLX', 
  `z` decimal(12,0) COMMENT 'Z', 
  `qjqs_year` decimal(10,0) COMMENT 'QJQS_YEAR', 
  `qjqs2_month` decimal(10,0) COMMENT 'QJQS2_MONTH', 
  `qjqs3_day` decimal(10,0) COMMENT 'QJQS3_DAY', 
  `qjjz_year` decimal(10,0) COMMENT 'QJJZ_YEAR', 
  `qjjz2_month` decimal(10,0) COMMENT 'QJJZ2_MONTH', 
  `qjjz3_day` decimal(10,0) COMMENT 'QJJZ3_DAY', 
  `jzrq` decimal(8,0) COMMENT 'JZRQ', 
  `zlsc_year` decimal(10,0) COMMENT 'ZLSC_YEAR', 
  `zlsc_month` decimal(10,0) COMMENT 'ZLSC_MONTH', 
  `zlsc_day` decimal(10,0) COMMENT 'ZLSC_DAY', 
  `mzqlx` decimal(12,0) COMMENT 'MZQLX', 
  `mzqsc` decimal(10,0) COMMENT 'MZQSC', 
  `mzqsc2` decimal(10,0) COMMENT 'MZQSC2', 
  `mzqsc3` decimal(10,0) COMMENT 'MZQSC3', 
  `mzq_year` decimal(10,0) COMMENT 'MZQ_YEAR', 
  `mzq_month` decimal(10,0) COMMENT 'MZQ_MONTH', 
  `mzq_day` decimal(10,0) COMMENT 'MZQ_DAY', 
  `mzq_zhi_year` decimal(16,0) COMMENT 'MZQ_ZHI_YEAR', 
  `mzq2_zhi_month` decimal(16,0) COMMENT 'MZQ2_ZHI_MONTH', 
  `mzq3_zhi_day` decimal(10,0) COMMENT 'MZQ3_ZHI_DAY', 
  `qzlx` decimal(12,0) COMMENT 'QZLX', 
  `qzr` decimal(8,0) COMMENT 'QZR', 
  `qzrq` string COMMENT 'QZRQ', 
  `zjzfyq` string COMMENT 'ZJZFYQ', 
  `zjtdfs` decimal(12,0) COMMENT 'ZJTDFS', 
  `qtzjtzfs` string COMMENT 'QTZJTZFS', 
  `yjzfyq` string COMMENT 'YJZFYQ', 
  `fyyd` decimal(38,0) COMMENT 'FYYD', 
  `cdfybcsm` string COMMENT 'CDFYBCSM', 
  `ytyq` string COMMENT 'YTYQ', 
  `qtsyytyq` string COMMENT 'QTSYYTYQ', 
  `sfyxzxgz` decimal(12,0) COMMENT 'SFYXZXGZ', 
  `zxgzbcsm` string COMMENT 'ZXGZBCSM', 
  `gpzj` decimal(16,2) COMMENT 'GPZJ', 
  `pjnzj` decimal(16,2) COMMENT 'PJNZJ', 
  `zdnts` decimal(16,2) COMMENT 'ZDNTS', 
  `zdyts` decimal(16,2) COMMENT 'ZDYTS', 
  `jnbzj` decimal(12,0) COMMENT 'JNBZJ', 
  `bzjje` decimal(16,2) COMMENT 'BZJJE', 
  `jnsj` string COMMENT 'JNSJ', 
  `jnfs` string COMMENT 'JNFS', 
  `bzjczfs` decimal(12,0) COMMENT 'BZJCZFS', 
  `tsxx` string COMMENT 'TSXX', 
  `bzjkctk` string COMMENT 'BZJKCTK', 
  `bzjsm` string COMMENT 'BZJSM', 
  `plkssjxx` decimal(12,0) COMMENT 'PLKSSJXX', 
  `plksrq` decimal(8,0) COMMENT 'PLKSRQ', 
  `plkssj` string COMMENT 'PLKSSJ', 
  `plggq` decimal(10,0) COMMENT 'PLGGQ', 
  `wyxsrf` decimal(12,0) COMMENT 'WYXSRF', 
  `pljsrq` decimal(8,0) COMMENT 'PLJSRQ', 
  `ydgzr` decimal(10,0) COMMENT 'YDGZR', 
  `yqzq` decimal(3,0) COMMENT 'YQZQ', 
  `lxfs` decimal(12,0) COMMENT 'LXFS', 
  `wljjlx` decimal(12,0) COMMENT 'WLJJLX', 
  `lxfazynr` string COMMENT 'LXFAZYNR', 
  `rlr` decimal(12,0) COMMENT 'RLR', 
  `rlbm` decimal(12,0) COMMENT 'RLBM', 
  `rlsj` timestamp COMMENT 'RLSJ', 
  `czxgqttj` string COMMENT 'CZXGQTTJ', 
  `czfzgtj` string COMMENT 'CZFZGTJ', 
  `xmbmfzr` string COMMENT 'XMBMFZR', 
  `bmfzrdh` string COMMENT 'BMFZRDH', 
  `sfzwwxshyxx` decimal(12,0) COMMENT 'SFZWWXSHYXX', 
  `xmrlzx` decimal(12,0) COMMENT 'XMRLZX', 
  `xmrlbm` decimal(12,0) COMMENT 'XMRLBM', 
  `sfdq` decimal(12,0) COMMENT 'SFDQ', 
  `fmobile` string COMMENT 'FMOBILE', 
  `dxjsr` string COMMENT 'DXJSR', 
  `dxjsrsj` string COMMENT 'DXJSRSJ', 
  `czfxxsfpl` decimal(12,0) COMMENT 'CZFXXSFPL', 
  `gjwjqsrq` decimal(8,0) COMMENT 'GJWJQSRQ', 
  `gjwjjzrq` decimal(8,0) COMMENT 'GJWJJZRQ', 
  `yxczfcrnr` string COMMENT 'YXCZFCRNR', 
  `gjwj` string COMMENT 'GJWJ', 
  `jcwj` string COMMENT 'JCWJ', 
  `yfwczht` string COMMENT 'YFWCZHT', 
  `flyjs` string COMMENT 'FLYJS', 
  `sqwts` string COMMENT 'SQWTS', 
  `qtwj` string COMMENT 'QTWJ', 
  `xxplsqs` string COMMENT 'XXPLSQS', 
  `xxplsqs_ydq` string COMMENT 'XXPLSQS_YDQ', 
  `xxplzjsqh` string COMMENT 'XXPLZJSQH', 
  `czzgqryjh` string COMMENT 'CZZGQRYJH', 
  `czzgqryjh_ydq` string COMMENT 'CZZGQRYJH_YDQ', 
  `cxplsqh` string COMMENT 'CXPLSQH', 
  `thyy` string COMMENT 'THYY', 
  `fwsyxz` decimal(12,0) COMMENT 'FWSYXZ', 
  `dqr` decimal(8,0) COMMENT 'DQR', 
  `qtwj_dfj` string COMMENT 'QTWJ_DFJ', 
  `cxplsqh_ydq` string COMMENT 'CXPLSQH_YDQ', 
  `xxplzjsqh_ydq` string COMMENT 'XXPLZJSQH_YDQ', 
  `xmzzsqh` string COMMENT 'XMZZSQH', 
  `xmzzsqh_ydq` string COMMENT 'XMZZSQH_YDQ', 
  `xmhfsqh` string COMMENT 'XMHFSQH', 
  `xmhfsqh_ydq` string COMMENT 'XMHFSQH_YDQ', 
  `xmzjsqh` string COMMENT 'XMZJSQH', 
  `xmzjsqh_ydq` string COMMENT 'XMZJSQH_YDQ', 
  `bzjjnjzsj` string COMMENT 'BZJJNJZSJ', 
  `zjggbt` string COMMENT 'ZJGGBT', 
  `zjggnr` string COMMENT 'ZJGGNR', 
  `czfczyh` decimal(12,0) COMMENT 'CZFCZYH', 
  `xmly` decimal(16,0) COMMENT 'XMLY', 
  `wb_xmid` string COMMENT 'WB_XMID', 
  `sfsjyxjyfw` decimal(12,0) COMMENT 'SFSJYXJYFW', 
  `xmzzggbt` string COMMENT 'XMZZGGBT', 
  `xmzzggnr` string COMMENT 'XMZZGGNR', 
  `xmzzzjhsysm` string COMMENT 'XMZZZJHSYSM', 
  `zzjzr` decimal(8,0) COMMENT 'ZZJZR', 
  `xmhfggbt` string COMMENT 'XMHFGGBT', 
  `xmhfggnr` string COMMENT 'XMHFGGNR', 
  `hfyy` string COMMENT 'HFYY', 
  `xmzjggbt` string COMMENT 'XMZJGGBT', 
  `xmzjggnr` string COMMENT 'XMZJGGNR', 
  `xmzjzjhsysm` string COMMENT 'XMZJZJHSYSM', 
  `pjyzj` decimal(16,2) COMMENT 'PJYZJ', 
  `zfyy` string COMMENT 'ZFYY', 
  `zfsj` timestamp COMMENT 'ZFSJ', 
  `czfztzgwj` string COMMENT 'CZFZTZGWJ', 
  `lssjbgjl` string COMMENT 'LSSJBGJL', 
  `zfr` decimal(12,0) COMMENT 'ZFR', 
  `sfdy_gp` decimal(12,0) COMMENT 'SFDY_GP', 
  `dysj_gp` timestamp COMMENT 'DYSJ_GP', 
  `sfdy_cz` decimal(12,0) COMMENT 'SFDY_CZ', 
  `dysj_cz` timestamp COMMENT 'DYSJ_CZ', 
  `sfdy_cj` decimal(12,0) COMMENT 'SFDY_CJ', 
  `dysj_cj` timestamp COMMENT 'DYSJ_CJ', 
  `pzrlzx` decimal(12,0) COMMENT 'PZRLZX', 
  `pzrlbm` decimal(12,0) COMMENT 'PZRLBM', 
  `pzxmjl` decimal(12,0) COMMENT 'PZXMJL', 
  `sfbjsdq` decimal(12,0) COMMENT 'SFBJSDQ', 
  `xmjl` decimal(12,0) COMMENT 'XMJL', 
  `zzksr` decimal(8,0) COMMENT 'ZZKSR', 
  `czfxxsfnmpl` decimal(12,0) COMMENT 'CZFXXSFNMPL', 
  `zjgpjsfbhqtfy` decimal(12,0) COMMENT 'ZJGPJSFBHQTFY', 
  `jyqytzs` string COMMENT 'JYQYTZS', 
  `jyqytzsydq` string COMMENT 'JYQYTZSYDQ', 
  `cpjjcxplhxmid` decimal(16,0) COMMENT 'CPJJCXPLHXMID', 
  `xxplzjxx` decimal(12,0) COMMENT 'XXPLZJXX', 
  `cpcxplyy` string COMMENT 'CPCXPLYY', 
  `nbjcqkmc` string COMMENT 'NBJCQKMC', 
  `sfczsjfpjl` decimal(12,0) COMMENT 'SFCZSJFPJL', 
  `glywfpjl` string COMMENT 'GLYWFPJL', 
  `fpbm` string COMMENT 'FPBM', 
  `nczmj` decimal(16,3) COMMENT 'NCZMJ', 
  `sfyyfyxx` decimal(12,0) COMMENT 'SFYYFYXX')
PARTITIONED BY ( 
  `dt` string)
ROW FORMAT SERDE 
  'org.apache.hadoop.hive.ql.io.orc.OrcSerde' 
WITH SERDEPROPERTIES ( 
  'colelction.delim'='', 
  'field.delim'=',', 
  'mapkey.delim'='', 
  'serialization.format'=',') 
STORED AS INPUTFORMAT 
  'org.apache.hadoop.hive.ql.io.orc.OrcInputFormat' 
OUTPUTFORMAT 
  'org.apache.hadoop.hive.ql.io.orc.OrcOutputFormat'
LOCATION
  'hdfs://cbex/user/hive/warehouse/ods.db/ods_bjhl_tzccz_zcczxm'
TBLPROPERTIES (
  'orc.compress'='SNAPPY', 
  'spark.sql.create.version'='2.2 or prior', 
  'spark.sql.sources.schema.numPartCols'='1', 
  'spark.sql.sources.schema.numParts'='5', 
  'spark.sql.sources.schema.part.0'='{"type":"struct","fields":[{"name":"id","type":"decimal(16,0)","nullable":true,"metadata":{"comment":"ID"}},{"name":"jbr","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"JBR"}},{"name":"xmfzr","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"XMFZR"}},{"name":"xmlxr","type":"string","nullable":true,"metadata":{"comment":"XMLXR"}},{"name":"xmlxrdh","type":"string","nullable":true,"metadata":{"comment":"XMLXRDH"}},{"name":"bmfzr","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"BMFZR"}},{"name":"xmssbm","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"XMSSBM"}},{"name":"jjr","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"JJR"}},{"name":"hyjg","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"HYJG"}},{"name":"hylxr","type":"string","nullable":true,"metadata":{"comment":"HYLXR"}},{"name":"hylxdh","type":"string","nullable":true,"metadata":{"comment":"HYLXDH"}},{"name":"sfcxgp","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"SFCXGP"}},{"name":"glypl","type":"decimal(16,0)","nullable":true,"metadata":{"comment":"GLYPL"}},{"name":"cjr","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"CJR"}},{"name":"cjsj","type":"timestamp","nullable":true,"metadata":{"comment":"CJSJ"}},{"name":"xgr","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"XGR"}},{"name":"xgsj","type":"timestamp","nullable":true,"metadata":{"comment":"XGSJ"}},{"name":"xmid","type":"decimal(16,0)","nullable":true,"metadata":{"comment":"XMID"}},{"name":"xzjyfs","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"XZJYFS"}},{"name":"xzjyfsrq","type":"timestamp","nullable":true,"metadata":{"comment":"XZJYFSRQ"}},{"name":"jyfsbgyy","type":"string","nullable":true,"metadata":{"comment":"JYFSBGYY"}},{"name":"cjrq","type":"string","nullable":true,"metadata":{"comment":"CJRQ"}},{"name":"cjjg","type":"string","nullable":true,"metadata":{"comment":"CJJG"}},{"name":"sjjyfs","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"SJJYFS"}},{"name":"zzrq","type":"decimal(8,0)","nullable":true,"metadata":{"comment":"ZZRQ"}},{"name":"zjrq","type":"decimal(8,0)","nullable":true,"metadata":{"comment":"ZJRQ"}},{"name":"sjpljssj","type":"timestamp","nullable":true,"metadata":{"comment":"SJPLJSSJ"}},{"name":"zcplrq","type":"decimal(8,0)","nullable":true,"metadata":{"comment":"ZCPLRQ"}},{"name":"xmlx","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"XMLX"}},{"name":"xmcjrs","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"XMCJRS"}},{"name":"xmmc","type":"string","nullable":true,"metadata":{"comment":"XMMC"}},{"name":"xmbh","type":"string","nullable":true,"metadata":{"comment":"XMBH"}},{"name":"bdmc","type":"string","nullable":true,"metadata":{"comment":"BDMC"}},{"name":"xmzt","type":"decimal(9,0)","nullable":true,"metadata":{"comment":"XMZT"}},{"name":"gyzczr","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"GYZCZR"}},{"name":"zclb","type":"decimal(38,0)","nullable":true,"metadata":{"comment":"ZCLB"}},{"name":"nbjcqk","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"NBJCQK"}},{"name":"nbjcqkqtsm","type":"string","nullable":true,"metadata":{"comment":"NBJCQKQTSM"}},{"name":"pzdw","type":"string","nullable":true,"metadata":{"comment":"PZDW"}},{"name":"pzwh","type":"string","nullable":true,"metadata":{"comment":"PZWH"}},{"name":"gjsfwwpl","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"GJSFWWPL"}},{"name":"gjdwmc","type":"string","nullable":true,"metadata":{"comment":"GJDWMC"}},{"name":"sffbgj","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"SFFBGJ"}},{"name":"zjgj","type":"decimal(16,2)","nullable":true,"metadata":{"comment":"ZJGJ"}},{"name":"gjdw","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"GJDW"}},{"name":"qtgjdw","type":"string","nullable":true,"metadata":{"comment":"QTGJDW"}},{"name":"yxczq","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"YXCZQ"}},{"nam', 
  'spark.sql.sources.schema.part.1'='e":"qtql","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"QTQL"}},{"name":"qtqlsm","type":"string","nullable":true,"metadata":{"comment":"QTQLSM"}},{"name":"qtplsx","type":"string","nullable":true,"metadata":{"comment":"QTPLSX"}},{"name":"xmfj","type":"string","nullable":true,"metadata":{"comment":"XMFJ"}},{"name":"xmtp","type":"string","nullable":true,"metadata":{"comment":"XMTP"}},{"name":"czfmc","type":"string","nullable":true,"metadata":{"comment":"CZFMC"}},{"name":"sffbjj","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"SFFBJJ"}},{"name":"czdj","type":"decimal(16,4)","nullable":true,"metadata":{"comment":"CZDJ"}},{"name":"czdjdw","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"CZDJDW"}},{"name":"qtdw","type":"string","nullable":true,"metadata":{"comment":"QTDW"}},{"name":"zjgpjbz","type":"string","nullable":true,"metadata":{"comment":"ZJGPJBZ"}},{"name":"nzjczflx","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"NZJCZFLX"}},{"name":"nzjczfgszs","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"NZJCZFGSZS"}},{"name":"nzjczfgszd","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"NZJCZFGSZD"}},{"name":"nzjczfgs","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"NZJCZFGS"}},{"name":"zlqlx","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"ZLQLX"}},{"name":"z","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"Z"}},{"name":"qjqs_year","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"QJQS_YEAR"}},{"name":"qjqs2_month","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"QJQS2_MONTH"}},{"name":"qjqs3_day","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"QJQS3_DAY"}},{"name":"qjjz_year","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"QJJZ_YEAR"}},{"name":"qjjz2_month","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"QJJZ2_MONTH"}},{"name":"qjjz3_day","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"QJJZ3_DAY"}},{"name":"jzrq","type":"decimal(8,0)","nullable":true,"metadata":{"comment":"JZRQ"}},{"name":"zlsc_year","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"ZLSC_YEAR"}},{"name":"zlsc_month","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"ZLSC_MONTH"}},{"name":"zlsc_day","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"ZLSC_DAY"}},{"name":"mzqlx","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"MZQLX"}},{"name":"mzqsc","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"MZQSC"}},{"name":"mzqsc2","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"MZQSC2"}},{"name":"mzqsc3","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"MZQSC3"}},{"name":"mzq_year","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"MZQ_YEAR"}},{"name":"mzq_month","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"MZQ_MONTH"}},{"name":"mzq_day","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"MZQ_DAY"}},{"name":"mzq_zhi_year","type":"decimal(16,0)","nullable":true,"metadata":{"comment":"MZQ_ZHI_YEAR"}},{"name":"mzq2_zhi_month","type":"decimal(16,0)","nullable":true,"metadata":{"comment":"MZQ2_ZHI_MONTH"}},{"name":"mzq3_zhi_day","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"MZQ3_ZHI_DAY"}},{"name":"qzlx","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"QZLX"}},{"name":"qzr","type":"decimal(8,0)","nullable":true,"metadata":{"comment":"QZR"}},{"name":"qzrq","type":"string","nullable":true,"metadata":{"comment":"QZRQ"}},{"name":"zjzfyq","type":"string","nullable":true,"metadata":{"comment":"ZJZFYQ"}},{"name":"zjtdfs","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"ZJTDFS"}},{"name":"qtzjtzfs","type":"string","nullable":true,"metadata":{"comment":"QTZJTZFS"}},{"name":"yjzfyq","type":"string","nullable":true,"metadata":{"comment":"YJZFYQ"}},{"name":"fyyd","type":"decimal(38,0)","nullable":true,"metadata":{"comment":"FYY', 
  'spark.sql.sources.schema.part.2'='D"}},{"name":"cdfybcsm","type":"string","nullable":true,"metadata":{"comment":"CDFYBCSM"}},{"name":"ytyq","type":"string","nullable":true,"metadata":{"comment":"YTYQ"}},{"name":"qtsyytyq","type":"string","nullable":true,"metadata":{"comment":"QTSYYTYQ"}},{"name":"sfyxzxgz","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"SFYXZXGZ"}},{"name":"zxgzbcsm","type":"string","nullable":true,"metadata":{"comment":"ZXGZBCSM"}},{"name":"gpzj","type":"decimal(16,2)","nullable":true,"metadata":{"comment":"GPZJ"}},{"name":"pjnzj","type":"decimal(16,2)","nullable":true,"metadata":{"comment":"PJNZJ"}},{"name":"zdnts","type":"decimal(16,2)","nullable":true,"metadata":{"comment":"ZDNTS"}},{"name":"zdyts","type":"decimal(16,2)","nullable":true,"metadata":{"comment":"ZDYTS"}},{"name":"jnbzj","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"JNBZJ"}},{"name":"bzjje","type":"decimal(16,2)","nullable":true,"metadata":{"comment":"BZJJE"}},{"name":"jnsj","type":"string","nullable":true,"metadata":{"comment":"JNSJ"}},{"name":"jnfs","type":"string","nullable":true,"metadata":{"comment":"JNFS"}},{"name":"bzjczfs","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"BZJCZFS"}},{"name":"tsxx","type":"string","nullable":true,"metadata":{"comment":"TSXX"}},{"name":"bzjkctk","type":"string","nullable":true,"metadata":{"comment":"BZJKCTK"}},{"name":"bzjsm","type":"string","nullable":true,"metadata":{"comment":"BZJSM"}},{"name":"plkssjxx","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"PLKSSJXX"}},{"name":"plksrq","type":"decimal(8,0)","nullable":true,"metadata":{"comment":"PLKSRQ"}},{"name":"plkssj","type":"string","nullable":true,"metadata":{"comment":"PLKSSJ"}},{"name":"plggq","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"PLGGQ"}},{"name":"wyxsrf","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"WYXSRF"}},{"name":"pljsrq","type":"decimal(8,0)","nullable":true,"metadata":{"comment":"PLJSRQ"}},{"name":"ydgzr","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"YDGZR"}},{"name":"yqzq","type":"decimal(3,0)","nullable":true,"metadata":{"comment":"YQZQ"}},{"name":"lxfs","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"LXFS"}},{"name":"wljjlx","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"WLJJLX"}},{"name":"lxfazynr","type":"string","nullable":true,"metadata":{"comment":"LXFAZYNR"}},{"name":"rlr","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"RLR"}},{"name":"rlbm","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"RLBM"}},{"name":"rlsj","type":"timestamp","nullable":true,"metadata":{"comment":"RLSJ"}},{"name":"czxgqttj","type":"string","nullable":true,"metadata":{"comment":"CZXGQTTJ"}},{"name":"czfzgtj","type":"string","nullable":true,"metadata":{"comment":"CZFZGTJ"}},{"name":"xmbmfzr","type":"string","nullable":true,"metadata":{"comment":"XMBMFZR"}},{"name":"bmfzrdh","type":"string","nullable":true,"metadata":{"comment":"BMFZRDH"}},{"name":"sfzwwxshyxx","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"SFZWWXSHYXX"}},{"name":"xmrlzx","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"XMRLZX"}},{"name":"xmrlbm","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"XMRLBM"}},{"name":"sfdq","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"SFDQ"}},{"name":"fmobile","type":"string","nullable":true,"metadata":{"comment":"FMOBILE"}},{"name":"dxjsr","type":"string","nullable":true,"metadata":{"comment":"DXJSR"}},{"name":"dxjsrsj","type":"string","nullable":true,"metadata":{"comment":"DXJSRSJ"}},{"name":"czfxxsfpl","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"CZFXXSFPL"}},{"name":"gjwjqsrq","type":"decimal(8,0)","nullable":true,"metadata":{"comment":"GJWJQSRQ"}},{"name":"gjwjjzrq","type":"decimal(8,0)","nullable":true,"metadata":{"comment":"GJWJJZRQ"}},{"name":"yxczfcrnr","type":"string","nullable":true,"metadata":{"comment":"YXCZFCRNR"}},{"name":"gjwj","type":"string","nul', 
  'spark.sql.sources.schema.part.3'='lable":true,"metadata":{"comment":"GJWJ"}},{"name":"jcwj","type":"string","nullable":true,"metadata":{"comment":"JCWJ"}},{"name":"yfwczht","type":"string","nullable":true,"metadata":{"comment":"YFWCZHT"}},{"name":"flyjs","type":"string","nullable":true,"metadata":{"comment":"FLYJS"}},{"name":"sqwts","type":"string","nullable":true,"metadata":{"comment":"SQWTS"}},{"name":"qtwj","type":"string","nullable":true,"metadata":{"comment":"QTWJ"}},{"name":"xxplsqs","type":"string","nullable":true,"metadata":{"comment":"XXPLSQS"}},{"name":"xxplsqs_ydq","type":"string","nullable":true,"metadata":{"comment":"XXPLSQS_YDQ"}},{"name":"xxplzjsqh","type":"string","nullable":true,"metadata":{"comment":"XXPLZJSQH"}},{"name":"czzgqryjh","type":"string","nullable":true,"metadata":{"comment":"CZZGQRYJH"}},{"name":"czzgqryjh_ydq","type":"string","nullable":true,"metadata":{"comment":"CZZGQRYJH_YDQ"}},{"name":"cxplsqh","type":"string","nullable":true,"metadata":{"comment":"CXPLSQH"}},{"name":"thyy","type":"string","nullable":true,"metadata":{"comment":"THYY"}},{"name":"fwsyxz","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"FWSYXZ"}},{"name":"dqr","type":"decimal(8,0)","nullable":true,"metadata":{"comment":"DQR"}},{"name":"qtwj_dfj","type":"string","nullable":true,"metadata":{"comment":"QTWJ_DFJ"}},{"name":"cxplsqh_ydq","type":"string","nullable":true,"metadata":{"comment":"CXPLSQH_YDQ"}},{"name":"xxplzjsqh_ydq","type":"string","nullable":true,"metadata":{"comment":"XXPLZJSQH_YDQ"}},{"name":"xmzzsqh","type":"string","nullable":true,"metadata":{"comment":"XMZZSQH"}},{"name":"xmzzsqh_ydq","type":"string","nullable":true,"metadata":{"comment":"XMZZSQH_YDQ"}},{"name":"xmhfsqh","type":"string","nullable":true,"metadata":{"comment":"XMHFSQH"}},{"name":"xmhfsqh_ydq","type":"string","nullable":true,"metadata":{"comment":"XMHFSQH_YDQ"}},{"name":"xmzjsqh","type":"string","nullable":true,"metadata":{"comment":"XMZJSQH"}},{"name":"xmzjsqh_ydq","type":"string","nullable":true,"metadata":{"comment":"XMZJSQH_YDQ"}},{"name":"bzjjnjzsj","type":"string","nullable":true,"metadata":{"comment":"BZJJNJZSJ"}},{"name":"zjggbt","type":"string","nullable":true,"metadata":{"comment":"ZJGGBT"}},{"name":"zjggnr","type":"string","nullable":true,"metadata":{"comment":"ZJGGNR"}},{"name":"czfczyh","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"CZFCZYH"}},{"name":"xmly","type":"decimal(16,0)","nullable":true,"metadata":{"comment":"XMLY"}},{"name":"wb_xmid","type":"string","nullable":true,"metadata":{"comment":"WB_XMID"}},{"name":"sfsjyxjyfw","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"SFSJYXJYFW"}},{"name":"xmzzggbt","type":"string","nullable":true,"metadata":{"comment":"XMZZGGBT"}},{"name":"xmzzggnr","type":"string","nullable":true,"metadata":{"comment":"XMZZGGNR"}},{"name":"xmzzzjhsysm","type":"string","nullable":true,"metadata":{"comment":"XMZZZJHSYSM"}},{"name":"zzjzr","type":"decimal(8,0)","nullable":true,"metadata":{"comment":"ZZJZR"}},{"name":"xmhfggbt","type":"string","nullable":true,"metadata":{"comment":"XMHFGGBT"}},{"name":"xmhfggnr","type":"string","nullable":true,"metadata":{"comment":"XMHFGGNR"}},{"name":"hfyy","type":"string","nullable":true,"metadata":{"comment":"HFYY"}},{"name":"xmzjggbt","type":"string","nullable":true,"metadata":{"comment":"XMZJGGBT"}},{"name":"xmzjggnr","type":"string","nullable":true,"metadata":{"comment":"XMZJGGNR"}},{"name":"xmzjzjhsysm","type":"string","nullable":true,"metadata":{"comment":"XMZJZJHSYSM"}},{"name":"pjyzj","type":"decimal(16,2)","nullable":true,"metadata":{"comment":"PJYZJ"}},{"name":"zfyy","type":"string","nullable":true,"metadata":{"comment":"ZFYY"}},{"name":"zfsj","type":"timestamp","nullable":true,"metadata":{"comment":"ZFSJ"}},{"name":"czfztzgwj","type":"string","nullable":true,"metadata":{"comment":"CZFZTZGWJ"}},{"name":"lssjbgjl","type":"string","nullable":true,"metadata":{"comment":"LSSJBGJL"}},{"name":"zfr","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"ZFR"}', 
  'spark.sql.sources.schema.part.4'='},{"name":"sfdy_gp","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"SFDY_GP"}},{"name":"dysj_gp","type":"timestamp","nullable":true,"metadata":{"comment":"DYSJ_GP"}},{"name":"sfdy_cz","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"SFDY_CZ"}},{"name":"dysj_cz","type":"timestamp","nullable":true,"metadata":{"comment":"DYSJ_CZ"}},{"name":"sfdy_cj","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"SFDY_CJ"}},{"name":"dysj_cj","type":"timestamp","nullable":true,"metadata":{"comment":"DYSJ_CJ"}},{"name":"pzrlzx","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"PZRLZX"}},{"name":"pzrlbm","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"PZRLBM"}},{"name":"pzxmjl","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"PZXMJL"}},{"name":"sfbjsdq","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"SFBJSDQ"}},{"name":"xmjl","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"XMJL"}},{"name":"zzksr","type":"decimal(8,0)","nullable":true,"metadata":{"comment":"ZZKSR"}},{"name":"czfxxsfnmpl","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"CZFXXSFNMPL"}},{"name":"zjgpjsfbhqtfy","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"ZJGPJSFBHQTFY"}},{"name":"jyqytzs","type":"string","nullable":true,"metadata":{"comment":"JYQYTZS"}},{"name":"jyqytzsydq","type":"string","nullable":true,"metadata":{"comment":"JYQYTZSYDQ"}},{"name":"cpjjcxplhxmid","type":"decimal(16,0)","nullable":true,"metadata":{"comment":"CPJJCXPLHXMID"}},{"name":"xxplzjxx","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"XXPLZJXX"}},{"name":"cpcxplyy","type":"string","nullable":true,"metadata":{"comment":"CPCXPLYY"}},{"name":"nbjcqkmc","type":"string","nullable":true,"metadata":{"comment":"NBJCQKMC"}},{"name":"sfczsjfpjl","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"SFCZSJFPJL"}},{"name":"glywfpjl","type":"string","nullable":true,"metadata":{"comment":"GLYWFPJL"}},{"name":"fpbm","type":"string","nullable":true,"metadata":{"comment":"FPBM"}},{"name":"nczmj","type":"decimal(16,3)","nullable":true,"metadata":{"comment":"NCZMJ"}},{"name":"sfyyfyxx","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"SFYYFYXX"}},{"name":"dt","type":"string","nullable":true,"metadata":{}}]}', 
  'spark.sql.sources.schema.partCol.0'='dt', 
  'transient_lastDdlTime'='1724935048')















  CREATE TABLE `ods.ods_bjhl_tzccz_cjjl`(
  `id` decimal(16,0) COMMENT 'ID', 
  `xm` decimal(16,0) COMMENT 'XM', 
  `xmid_fgk` decimal(16,0) COMMENT 'XMID_FGK', 
  `srf` decimal(16,0) COMMENT 'SRF', 
  `jypzdyys` decimal(3,0) COMMENT 'JYPZDYYS', 
  `npjzj` decimal(16,2) COMMENT 'NPJZJ', 
  `sffbjj` decimal(12,0) COMMENT 'SFFBJJ', 
  `cjjg` string COMMENT 'CJJG', 
  `zjcjj` decimal(16,2) COMMENT 'ZJCJJ', 
  `cjjdw` decimal(12,0) COMMENT 'CJJDW', 
  `qtdw` string COMMENT 'QTDW', 
  `cjzjjgsm` string COMMENT 'CJZJJGSM', 
  `zlsc_year` decimal(10,0) COMMENT 'ZLSC_YEAR', 
  `zlsc_month` decimal(10,0) COMMENT 'ZLSC_MONTH', 
  `zlsc_day` decimal(10,0) COMMENT 'ZLSC_DAY', 
  `mzqsc` decimal(10,0) COMMENT 'MZQSC', 
  `mzqsc2` decimal(10,0) COMMENT 'MZQSC2', 
  `mzqsc3` decimal(10,0) COMMENT 'MZQSC3', 
  `qzr` decimal(8,0) COMMENT 'QZR', 
  `dqr` decimal(8,0) COMMENT 'DQR', 
  `zjtdfs` decimal(12,0) COMMENT 'ZJTDFS', 
  `zjzfyq` string COMMENT 'ZJZFYQ', 
  `qtzjtzfs` string COMMENT 'QTZJTZFS', 
  `bzjje` decimal(16,2) COMMENT 'BZJJE', 
  `sfzjk` decimal(12,0) COMMENT 'SFZJK', 
  `zjkje` decimal(16,2) COMMENT 'ZJKJE', 
  `zffs` decimal(12,0) COMMENT 'ZFFS', 
  `sfje` decimal(16,2) COMMENT 'SFJE', 
  `sqfkbl` decimal(7,4) COMMENT 'SQFKBL', 
  `wkjzrq` timestamp COMMENT 'WKJZRQ', 
  `jkje` decimal(16,2) COMMENT 'JKJE', 
  `htqdrq` timestamp COMMENT 'HTQDRQ', 
  `htsxrq` timestamp COMMENT 'HTSXRQ', 
  `jsfs` decimal(12,0) COMMENT 'JSFS', 
  `sfwbjs` decimal(12,0) COMMENT 'SFWBJS', 
  `wbbz` string COMMENT 'WBBZ', 
  `ydrhl` decimal(9,4) COMMENT 'YDRHL', 
  `zswbje` decimal(16,2) COMMENT 'ZSWBJE', 
  `jyfs` decimal(12,0) COMMENT 'JYFS', 
  `sjjyfs` decimal(12,0) COMMENT 'SJJYFS', 
  `wljjlx` decimal(12,0) COMMENT 'WLJJLX', 
  `ggjyfsyy` string COMMENT 'GGJYFSYY', 
  `jyjgshyj` string COMMENT 'JYJGSHYJ', 
  `sfkdy` decimal(12,0) COMMENT 'SFKDY', 
  `jggsksrq` decimal(8,0) COMMENT 'JGGSKSRQ', 
  `jggsjsrq` decimal(8,0) COMMENT 'JGGSJSRQ', 
  `jggszq` decimal(10,0) COMMENT 'JGGSZQ', 
  `cjrq` decimal(8,0) COMMENT 'CJRQ', 
  `dzjkje` decimal(16,2) COMMENT 'DZJKJE', 
  `bzjzjkje` decimal(16,2) COMMENT 'BZJZJKJE', 
  `hcbs` decimal(12,0) COMMENT 'HCBS', 
  `hcje` decimal(16,2) COMMENT 'HCJE', 
  `bz` string COMMENT 'BZ', 
  `cjr` decimal(12,0) COMMENT 'CJR', 
  `cjsj` timestamp COMMENT 'CJSJ', 
  `gxr` decimal(12,0) COMMENT 'GXR', 
  `gxsj` timestamp COMMENT 'GXSJ', 
  `cjzt` decimal(12,0) COMMENT 'CJZT', 
  `cjjlbh` string COMMENT 'CJJLBH', 
  `bzjzjkddid` string COMMENT 'BZJZJKDDID', 
  `yjje` decimal(16,2) COMMENT 'YJJE', 
  `zjddzt` decimal(12,0) COMMENT 'ZJDDZT', 
  `ydksj` decimal(16,2) COMMENT 'YDKSJ', 
  `jsfs_yj` decimal(12,0) COMMENT 'JSFS_YJ', 
  `fwczjypz` string COMMENT 'FWCZJYPZ', 
  `fwczjypz_ydq` string COMMENT 'FWCZJYPZ_YDQ', 
  `jypz` timestamp COMMENT 'JYPZ', 
  `zj` string COMMENT 'ZJ', 
  `pjnzj` string COMMENT 'PJNZJ', 
  `pjyzj` string COMMENT 'PJYZJ', 
  `zdnts` decimal(16,2) COMMENT 'ZDNTS', 
  `zdyts` decimal(16,2) COMMENT 'ZDYTS', 
  `sfsfwf` decimal(12,0) COMMENT 'SFSFWF', 
  `glcjxxlc` string COMMENT 'GLCJXXLC', 
  `glhtxx` decimal(16,0) COMMENT 'GLHTXX', 
  `sfdq` decimal(12,0) COMMENT 'SFDQ', 
  `fwczcjshyj` string COMMENT 'FWCZCJSHYJ', 
  `fwczcjshyj_ydq` string COMMENT 'FWCZCJSHYJ_YDQ', 
  `fwczht` string COMMENT 'FWCZHT', 
  `fwczht_ydq` string COMMENT 'FWCZHT_YDQ', 
  `xmfzr` decimal(12,0) COMMENT 'XMFZR', 
  `xmfzrtgsj` decimal(8,0) COMMENT 'XMFZRTGSJ', 
  `ywbmfzr` decimal(12,0) COMMENT 'YWBMFZR', 
  `ywbmfzrtgsj` decimal(8,0) COMMENT 'YWBMFZRTGSJ', 
  `jyshbshr` decimal(12,0) COMMENT 'JYSHBSHR', 
  `jyshbshrtgsj` decimal(8,0) COMMENT 'JYSHBSHRTGSJ', 
  `jybfzr` decimal(12,0) COMMENT 'JYBFZR', 
  `jybfzrtgsj` decimal(8,0) COMMENT 'JYBFZRTGSJ', 
  `jysshr` decimal(12,0) COMMENT 'JYSSHR', 
  `jysshrtgsj` decimal(8,0) COMMENT 'JYSSHRTGSJ', 
  `ypjzjjg` decimal(16,2) COMMENT 'YPJZJJG', 
  `cjzjdj` decimal(10,2) COMMENT 'CJZJDJ', 
  `bzjczfs` decimal(12,0) COMMENT 'BZJCZFS', 
  `xmdz` string COMMENT 'XMDZ', 
  `zxfzr` decimal(12,0) COMMENT 'ZXFZR', 
  `zxfzrtgsj` decimal(8,0) COMMENT 'ZXFZRTGSJ', 
  `cjjlbs` decimal(12,0) COMMENT 'CJJLBS', 
  `czmj` decimal(16,3) COMMENT 'CZMJ')
PARTITIONED BY ( 
  `dt` string)
ROW FORMAT SERDE 
  'org.apache.hadoop.hive.ql.io.orc.OrcSerde' 
WITH SERDEPROPERTIES ( 
  'colelction.delim'='', 
  'field.delim'=',', 
  'mapkey.delim'='', 
  'serialization.format'=',') 
STORED AS INPUTFORMAT 
  'org.apache.hadoop.hive.ql.io.orc.OrcInputFormat' 
OUTPUTFORMAT 
  'org.apache.hadoop.hive.ql.io.orc.OrcOutputFormat'
LOCATION
  'hdfs://cbex/user/hive/warehouse/ods.db/ods_bjhl_tzccz_cjjl'
TBLPROPERTIES (
  'orc.compress'='SNAPPY', 
  'spark.sql.create.version'='2.2 or prior', 
  'spark.sql.sources.schema.numPartCols'='1', 
  'spark.sql.sources.schema.numParts'='3', 
  'spark.sql.sources.schema.part.0'='{"type":"struct","fields":[{"name":"id","type":"decimal(16,0)","nullable":true,"metadata":{"comment":"ID"}},{"name":"xm","type":"decimal(16,0)","nullable":true,"metadata":{"comment":"XM"}},{"name":"xmid_fgk","type":"decimal(16,0)","nullable":true,"metadata":{"comment":"XMID_FGK"}},{"name":"srf","type":"decimal(16,0)","nullable":true,"metadata":{"comment":"SRF"}},{"name":"jypzdyys","type":"decimal(3,0)","nullable":true,"metadata":{"comment":"JYPZDYYS"}},{"name":"npjzj","type":"decimal(16,2)","nullable":true,"metadata":{"comment":"NPJZJ"}},{"name":"sffbjj","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"SFFBJJ"}},{"name":"cjjg","type":"string","nullable":true,"metadata":{"comment":"CJJG"}},{"name":"zjcjj","type":"decimal(16,2)","nullable":true,"metadata":{"comment":"ZJCJJ"}},{"name":"cjjdw","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"CJJDW"}},{"name":"qtdw","type":"string","nullable":true,"metadata":{"comment":"QTDW"}},{"name":"cjzjjgsm","type":"string","nullable":true,"metadata":{"comment":"CJZJJGSM"}},{"name":"zlsc_year","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"ZLSC_YEAR"}},{"name":"zlsc_month","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"ZLSC_MONTH"}},{"name":"zlsc_day","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"ZLSC_DAY"}},{"name":"mzqsc","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"MZQSC"}},{"name":"mzqsc2","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"MZQSC2"}},{"name":"mzqsc3","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"MZQSC3"}},{"name":"qzr","type":"decimal(8,0)","nullable":true,"metadata":{"comment":"QZR"}},{"name":"dqr","type":"decimal(8,0)","nullable":true,"metadata":{"comment":"DQR"}},{"name":"zjtdfs","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"ZJTDFS"}},{"name":"zjzfyq","type":"string","nullable":true,"metadata":{"comment":"ZJZFYQ"}},{"name":"qtzjtzfs","type":"string","nullable":true,"metadata":{"comment":"QTZJTZFS"}},{"name":"bzjje","type":"decimal(16,2)","nullable":true,"metadata":{"comment":"BZJJE"}},{"name":"sfzjk","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"SFZJK"}},{"name":"zjkje","type":"decimal(16,2)","nullable":true,"metadata":{"comment":"ZJKJE"}},{"name":"zffs","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"ZFFS"}},{"name":"sfje","type":"decimal(16,2)","nullable":true,"metadata":{"comment":"SFJE"}},{"name":"sqfkbl","type":"decimal(7,4)","nullable":true,"metadata":{"comment":"SQFKBL"}},{"name":"wkjzrq","type":"timestamp","nullable":true,"metadata":{"comment":"WKJZRQ"}},{"name":"jkje","type":"decimal(16,2)","nullable":true,"metadata":{"comment":"JKJE"}},{"name":"htqdrq","type":"timestamp","nullable":true,"metadata":{"comment":"HTQDRQ"}},{"name":"htsxrq","type":"timestamp","nullable":true,"metadata":{"comment":"HTSXRQ"}},{"name":"jsfs","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"JSFS"}},{"name":"sfwbjs","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"SFWBJS"}},{"name":"wbbz","type":"string","nullable":true,"metadata":{"comment":"WBBZ"}},{"name":"ydrhl","type":"decimal(9,4)","nullable":true,"metadata":{"comment":"YDRHL"}},{"name":"zswbje","type":"decimal(16,2)","nullable":true,"metadata":{"comment":"ZSWBJE"}},{"name":"jyfs","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"JYFS"}},{"name":"sjjyfs","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"SJJYFS"}},{"name":"wljjlx","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"WLJJLX"}},{"name":"ggjyfsyy","type":"string","nullable":true,"metadata":{"comment":"GGJYFSYY"}},{"name":"jyjgshyj","type":"string","nullable":true,"metadata":{"comment":"JYJGSHYJ"}},{"name":"sfkdy","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"SFKDY"}},{"name":"jggsksrq","type":"decimal(8,0)","nullable":true,"metadata":{"comment":"JGGSKSRQ"}},{"name":"jggsjsrq","type":"decimal(8,0)","nullable":true,"metadata":{"comment":"', 
  'spark.sql.sources.schema.part.1'='JGGSJSRQ"}},{"name":"jggszq","type":"decimal(10,0)","nullable":true,"metadata":{"comment":"JGGSZQ"}},{"name":"cjrq","type":"decimal(8,0)","nullable":true,"metadata":{"comment":"CJRQ"}},{"name":"dzjkje","type":"decimal(16,2)","nullable":true,"metadata":{"comment":"DZJKJE"}},{"name":"bzjzjkje","type":"decimal(16,2)","nullable":true,"metadata":{"comment":"BZJZJKJE"}},{"name":"hcbs","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"HCBS"}},{"name":"hcje","type":"decimal(16,2)","nullable":true,"metadata":{"comment":"HCJE"}},{"name":"bz","type":"string","nullable":true,"metadata":{"comment":"BZ"}},{"name":"cjr","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"CJR"}},{"name":"cjsj","type":"timestamp","nullable":true,"metadata":{"comment":"CJSJ"}},{"name":"gxr","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"GXR"}},{"name":"gxsj","type":"timestamp","nullable":true,"metadata":{"comment":"GXSJ"}},{"name":"cjzt","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"CJZT"}},{"name":"cjjlbh","type":"string","nullable":true,"metadata":{"comment":"CJJLBH"}},{"name":"bzjzjkddid","type":"string","nullable":true,"metadata":{"comment":"BZJZJKDDID"}},{"name":"yjje","type":"decimal(16,2)","nullable":true,"metadata":{"comment":"YJJE"}},{"name":"zjddzt","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"ZJDDZT"}},{"name":"ydksj","type":"decimal(16,2)","nullable":true,"metadata":{"comment":"YDKSJ"}},{"name":"jsfs_yj","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"JSFS_YJ"}},{"name":"fwczjypz","type":"string","nullable":true,"metadata":{"comment":"FWCZJYPZ"}},{"name":"fwczjypz_ydq","type":"string","nullable":true,"metadata":{"comment":"FWCZJYPZ_YDQ"}},{"name":"jypz","type":"timestamp","nullable":true,"metadata":{"comment":"JYPZ"}},{"name":"zj","type":"string","nullable":true,"metadata":{"comment":"ZJ"}},{"name":"pjnzj","type":"string","nullable":true,"metadata":{"comment":"PJNZJ"}},{"name":"pjyzj","type":"string","nullable":true,"metadata":{"comment":"PJYZJ"}},{"name":"zdnts","type":"decimal(16,2)","nullable":true,"metadata":{"comment":"ZDNTS"}},{"name":"zdyts","type":"decimal(16,2)","nullable":true,"metadata":{"comment":"ZDYTS"}},{"name":"sfsfwf","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"SFSFWF"}},{"name":"glcjxxlc","type":"string","nullable":true,"metadata":{"comment":"GLCJXXLC"}},{"name":"glhtxx","type":"decimal(16,0)","nullable":true,"metadata":{"comment":"GLHTXX"}},{"name":"sfdq","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"SFDQ"}},{"name":"fwczcjshyj","type":"string","nullable":true,"metadata":{"comment":"FWCZCJSHYJ"}},{"name":"fwczcjshyj_ydq","type":"string","nullable":true,"metadata":{"comment":"FWCZCJSHYJ_YDQ"}},{"name":"fwczht","type":"string","nullable":true,"metadata":{"comment":"FWCZHT"}},{"name":"fwczht_ydq","type":"string","nullable":true,"metadata":{"comment":"FWCZHT_YDQ"}},{"name":"xmfzr","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"XMFZR"}},{"name":"xmfzrtgsj","type":"decimal(8,0)","nullable":true,"metadata":{"comment":"XMFZRTGSJ"}},{"name":"ywbmfzr","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"YWBMFZR"}},{"name":"ywbmfzrtgsj","type":"decimal(8,0)","nullable":true,"metadata":{"comment":"YWBMFZRTGSJ"}},{"name":"jyshbshr","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"JYSHBSHR"}},{"name":"jyshbshrtgsj","type":"decimal(8,0)","nullable":true,"metadata":{"comment":"JYSHBSHRTGSJ"}},{"name":"jybfzr","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"JYBFZR"}},{"name":"jybfzrtgsj","type":"decimal(8,0)","nullable":true,"metadata":{"comment":"JYBFZRTGSJ"}},{"name":"jysshr","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"JYSSHR"}},{"name":"jysshrtgsj","type":"decimal(8,0)","nullable":true,"metadata":{"comment":"JYSSHRTGSJ"}},{"name":"ypjzjjg","type":"decimal(16,2)","nullable":true,"metadata":{"comment":"YPJZJJG"}},{"name":"cjzjdj","type":"decimal(10,2)","nullable"', 
  'spark.sql.sources.schema.part.2'=':true,"metadata":{"comment":"CJZJDJ"}},{"name":"bzjczfs","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"BZJCZFS"}},{"name":"xmdz","type":"string","nullable":true,"metadata":{"comment":"XMDZ"}},{"name":"zxfzr","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"ZXFZR"}},{"name":"zxfzrtgsj","type":"decimal(8,0)","nullable":true,"metadata":{"comment":"ZXFZRTGSJ"}},{"name":"cjjlbs","type":"decimal(12,0)","nullable":true,"metadata":{"comment":"CJJLBS"}},{"name":"czmj","type":"decimal(16,3)","nullable":true,"metadata":{"comment":"CZMJ"}},{"name":"dt","type":"string","nullable":true,"metadata":{}}]}', 
  'spark.sql.sources.schema.partCol.0'='dt', 
  'transient_lastDdlTime'='1724934902')