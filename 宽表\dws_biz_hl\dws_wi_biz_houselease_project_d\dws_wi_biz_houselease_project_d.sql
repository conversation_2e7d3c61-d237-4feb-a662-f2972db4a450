with tzccz_zcczxm as (
select
	distinct
    t1.bsn_prj_wrd,
    t1.bsn_prj_id,
    t1.project_code, -- 项目编号
    t1.project_name, -- 项目名称
    t1.lessor_nm, -- 出租方名称
    t1.rent_lst_prc, -- 挂牌价格（元）
    CASE WHEN T1.prj_prc_unit_dsc = '元/平方米/天' THEN T1.rent_lst_prc
             WHEN T1.prj_prc_unit_dsc = '元/平方米/月' THEN T1.rent_lst_prc / 30
             WHEN T1.prj_prc_unit_dsc = '元/月' THEN T1.rent_lst_prc / (T1.plan_lease_area * 30)
             WHEN T1.prj_prc_unit_dsc = '元/年' THEN T1.rent_lst_prc / (T1.plan_lease_area * 365) 
          ELSE NULL 
        END      AS list_price_std_sq_d,                           -- 挂牌价格_标准化（元/平方米/天）
    CASE WHEN T1.prj_prc_unit_dsc = '元/平方米/天' THEN T1.rnt_fee_est_yuan
             WHEN T1.prj_prc_unit_dsc = '元/平方米/月' THEN T1.rnt_fee_est_yuan / 30
             WHEN T1.prj_prc_unit_dsc = '元/月' THEN T1.rnt_fee_est_yuan / (T1.plan_lease_area * 30)
             WHEN T1.prj_prc_unit_dsc = '元/年' THEN T1.rnt_fee_est_yuan / (T1.plan_lease_area * 365) 
          ELSE NULL 
        END      AS appr_price_std_sq_d,                           -- 估价价格_标准化（元/平方米/天）
    t1.prj_prc_unit_dsc, -- 挂牌价格单位
    t1.rent_lit_tot_prc, -- 租金挂牌总价(元)
    t1.plan_lease_area, -- 拟出租面积(平方米)
    t1.esr_strt_dt, -- 披露开始日期
    t1.rnt_fee_est_yuan, -- 租金估价(元)
    t1.esr_end_dt, -- 披露结束日期
    t1.prj_sts_dsc, -- 项目状态
    t31.usr_nm as prj_pnp, -- 项目负责人
    t26.org_nm as prj_blng_dept, -- 项目所属部门
    t27.org_nm as prj_clm_dept_nm, -- 项目认领部门
    t1.pick_mod_dsc, -- 遴选方式
    t1.ntw_bid_tp_dsc, -- 网络竞价类型
    t1.deposit_yuan, -- 保证金金额(元)
    t1.ast_cgy_dsc, -- 资产类别
    t1.nbjcqkmc, -- 内部决策情况名称
    t1.project_type_dsc, -- 项目类型
    t1.authorize_unit, -- 批准单位
    t1.est_unit_nm, -- 估价单位名称
    t1.est_unit_dsc, -- 估价单位
    t1.est_file_avl_dt_beg, -- 估价文件有效期起始
    t1.est_file_avl_dt_ct_of, -- 估价文件有效期截止
    t1.is_exst_prty_rentw, -- 是否存在优先承租权
    t1.rnt_fee_prj_prc_rmrk, -- 租金挂牌价备注
    t1.pcolt_lhder_cnt, -- 拟征集承租方个数
    t1.rent_postulate, -- 承租方资格条件
    t1.mbsh_org, -- 会员机构
    t1.deal_mth_cd_dsc, -- 成交方式
    t1.use_rqmt_cd_dsc, -- 房屋使用用途
    t1.is_nal_hs_rent, -- 是否国有房屋出租
    t1.lestm_tp_cd_dsc, --租赁期类型
    t1.rng_tp_cd_dsc, --租赁期区间类型
    t1.least, -- 最少年
    t1.least_2, -- 最少2月
    t1.least_3, -- 最少3日
    t1.most, -- 最多年
    t1.most_2, -- 最多2月
    t1.most_3, -- 最多3日
    t1.lease_tm_legth, -- 租赁时长
    t1.lease_tm_legth_2, -- 租赁时长2
    t1.lease_tm_legth_3, -- 租赁时长3
    t1.frtpd_tp_cd_dsc, -- 免租期类型
    t1.frtpd_tm_legth AS frtpd_tm_legth, -- 免租期时长
    t1.frtpd_tm_legth_2 AS frtpd_tm_legth_2, -- 免租期时长2
    t1.frtpd_tm_legth_3 AS frtpd_tm_legth_3, -- 免租期时长3
    t1.frtpd AS frtpd, -- 免租期
    t1.frtpd_2 AS frtpd_2, -- 免租期2
    t1.frtpd_3 AS frtpd_3, -- 免租期3
    t1.frtpd_to AS frtpd_to, -- 免租期_至
    t1.frtpd_to_2 AS frtpd_to_2, -- 免租期2_至
    t1.frtpd_to_3 AS frtpd_to_3, -- 免租期3_至
    t1.expi_dt, --租赁截至日期
    -- 挂牌租赁期_标准化（天）
    case 
        when 
            t1.lestm_tp_cd_dsc = '固定值' 
            and t1.lease_tm_legth < 100 
        then 
            COALESCE(t1.lease_tm_legth, 0) * 365 + 
            COALESCE(t1.lease_tm_legth_2, 0) * 30 + 
            COALESCE(t1.lease_tm_legth_3, 0)
        when 
            t1.lestm_tp_cd_dsc = '区间' 
            and t1.rng_tp_cd_dsc IN ('最少~最多','最少/截止日期') 
            and (t1.least >= 100 or t1.most >= 100) 
        then 
            null 
        when 
            t1.lestm_tp_cd_dsc = '区间' 
            and t1.rng_tp_cd_dsc IN ('最少~最多','最少/截止日期') 
            and GREATEST(
                COALESCE(t1.least, 0) * 365 + COALESCE(t1.least_2, 0) * 30 + COALESCE(t1.least_3, 0),
                COALESCE(t1.most, 0) * 365 + COALESCE(t1.most_2, 0) * 30 + COALESCE(t1.most_3, 0)
            ) > 0 
        then 
            GREATEST(
                COALESCE(t1.least, 0) * 365 + COALESCE(t1.least_2, 0) * 30 + COALESCE(t1.least_3, 0),
                COALESCE(t1.most, 0) * 365 + COALESCE(t1.most_2, 0) * 30 + COALESCE(t1.most_3, 0)
            )
        when 
            t1.lestm_tp_cd_dsc = '区间' 
            and t1.rng_tp_cd_dsc = '截止日期' 
            and t1.lease_tm_legth >= 100 
            and (t1.least >= 100 or t1.most >= 100) 
        then 
            null 
        when 
            t1.lestm_tp_cd_dsc = '区间' 
            and t1.rng_tp_cd_dsc = '截止日期' 
            and t1.lease_tm_legth < 100 
            and (
                COALESCE(t1.lease_tm_legth, 0) * 365 + 
                COALESCE(t1.lease_tm_legth_2, 0) * 30 + 
                COALESCE(t1.lease_tm_legth_3, 0)
            ) > 0 
        then 
            COALESCE(t1.lease_tm_legth, 0) * 365 + 
            COALESCE(t1.lease_tm_legth_2, 0) * 30 + 
            COALESCE(t1.lease_tm_legth_3, 0)
        when 
            t1.lestm_tp_cd_dsc = '区间' 
            and t1.rng_tp_cd_dsc = '截止日期' 
            and (t1.lease_tm_legth >= 100 or nvl((COALESCE(t1.lease_tm_legth, 0) * 365 + COALESCE(t1.lease_tm_legth_2, 0) * 30 + COALESCE(t1.lease_tm_legth_3, 0)), 0) = 0) 
            and t1.least < 100 
            and t1.most < 100
            and GREATEST(
                COALESCE(t1.least, 0) * 365 + COALESCE(t1.least_2, 0) * 30 + COALESCE(t1.least_3, 0),
                COALESCE(t1.most, 0) * 365 + COALESCE(t1.most_2, 0) * 30 + COALESCE(t1.most_3, 0)
            ) > 0 
        then 
            GREATEST(
                COALESCE(t1.least, 0) * 365 + COALESCE(t1.least_2, 0) * 30 + COALESCE(t1.least_3, 0),
                COALESCE(t1.most, 0) * 365 + COALESCE(t1.most_2, 0) * 30 + COALESCE(t1.most_3, 0)
            )
        when 
            t1.lestm_tp_cd_dsc = '区间' 
            and t1.rng_tp_cd_dsc = '限制最小值' 
            and t1.least < 100 
        then 
            COALESCE(t1.least, 0) * 365 + 
            COALESCE(t1.least_2, 0) * 30 + 
            COALESCE(t1.least_3, 0) 
        else 
            null 
    end as lease_prd_std_d, -- 挂牌租赁期_标准化（天）
    -- 免租期_标准化（天）
    case 
        when 
            (t1.frtpd_tp_cd_dsc in ('以合同签订为准', '无') or t1.frtpd_tp_cd_dsc is null) 
        then 0
        when 
            t1.frtpd_tp_cd_dsc = '固定值' 
            and t1.frtpd_tm_legth >= 100  
        then 
            0
        when 
            t1.frtpd_tp_cd_dsc = '固定值' 
            and t1.frtpd_tm_legth between 0 and 99
        then 
            COALESCE(t1.frtpd_tm_legth, 0) * 365 + 
            COALESCE(t1.frtpd_tm_legth_2, 0) * 30 + 
            COALESCE(t1.frtpd_tm_legth_3, 0)
        when 
            t1.frtpd_tp_cd_dsc = '区间' 
            and t1.frtpd between 0 and 10 
            and t1.frtpd_to between 0 and 10 
        then 
            greatest(
                COALESCE(t1.frtpd, 0) * 360 + COALESCE(t1.frtpd_2, 0) * 30 + COALESCE(t1.frtpd_3, 0),
                COALESCE(t1.frtpd_to, 0) * 360 + COALESCE(t1.frtpd_to_2, 0) * 30 + COALESCE(t1.frtpd_to_3, 0)
            )
        when 
            t1.frtpd_tp_cd_dsc = '区间' 
            and t1.frtpd >= 2010 
            and t1.frtpd_to >= 2010 
        then 
            abs(
                (COALESCE(t1.frtpd_to, 0) * 360 + COALESCE(t1.frtpd_to_2, 0) * 30 + COALESCE(t1.frtpd_to_3, 0)) -
                (COALESCE(t1.frtpd, 0) * 360 + COALESCE(t1.frtpd_2, 0) * 30 + COALESCE(t1.frtpd_3, 0))
            )
        else 0
    end as free_prd_std_d, -- 免租期_标准化（天）
    t1.is_resp_val_dsc -- 0|统一计价；1|分别计价；2|面议
from dwd.dwd_ast_lease_prj_fct t1
LEFT JOIN  (
SELECT 
    id  --代码 
    ,usr_nm  --码值
FROM  std.std_bjhl_tuser_d  --用户管理
WHERE  dt='${dmp_day}') t31                          
ON t1.prj_pnp=t31.ID
LEFT JOIN (
SELECT 
    id  ,--代码 
    org_nm  --码值
FROM std.std_bjhl_lborganization_d --组织机构
WHERE dt = '${dmp_day}' and org_nm LIKE '%市属%') t26
ON t1.prj_blng_dept = t26.id
LEFT JOIN (
SELECT 
    id  ,--代码 
    org_nm  --码值
FROM std.std_bjhl_lborganization_d --组织机构
WHERE dt = '${dmp_day}') t27
ON t1.prj_clm_dept_id = t27.id
where dt = '${dmp_day}' and t1.project_code is not NULL
and (t1.project_code not like 'GL0%' and t1.project_code not like 'TL0%')
),
tzccz_cjjl as (
 select 
    bsn_prj_wrd,      --业务项目关键字
    lease_tm_legth,           -- 年 
    lease_tm_legth2,          -- 月 
    lease_tm_legth3,          -- 日
    sevly_valut_cd_dsc,
    lease_tm_legth*12+lease_tm_legth2+lease_tm_legth3/30 as project_price_m, -- 挂牌价格（元/平米/月）
    lease_tm_legth+lease_tm_legth2/12+lease_tm_legth3/365 as project_price_y, -- 挂牌价格（元/平米/年）   
    case when deal_date is not null then concat(lease_tm_legth,'年',lease_tm_legth2,'月',lease_tm_legth3,'日') end as publish_date, -- 挂牌租期
    case when deal_date is not null then concat(lease_tm_legth,'年',lease_tm_legth2,'月',lease_tm_legth3,'日') end as lease_tm, -- 租赁期
    case when deal_date is not null then (lease_tm_legth*365+lease_tm_legth2*30+lease_tm_legth3)  end as deal_lease_prd_std_d, -- 租赁时长_成交标准化（天）

    deal_rent_prc as deal_price, -- 成交租金价格
    deal_rent_prc_unit_cmnt as deal_rent_unit, -- 成交租金价格单位说明
    lease_area,                -- 出租面积 
    deal_total_price/(lease_tm_legth+lease_tm_legth2/12+lease_tm_legth3/365)/lease_area as deal_price_metr_y, -- 成交价格（元/平米/年）
    deal_total_price/(lease_tm_legth*12+lease_tm_legth2+lease_tm_legth3/30)/lease_area as deal_price_metr_m, -- 成交价格（元/平米/月）
    deal_total_price/(lease_tm_legth*365+lease_tm_legth2*30+lease_tm_legth3)/lease_area as deal_rent_metr_unit, -- 成交租金单价(元/平方米/天)  
    deal_total_price/(lease_tm_legth*12+lease_tm_legth2+lease_tm_legth3/30) as deal_price_m, -- 成交价格（月）
    deal_total_price/(lease_tm_legth+lease_tm_legth2/12+lease_tm_legth3/365) as deal_price_y, -- 成交价格（年）
    deal_total_price, -- 成交总价（元）
    lease_tm_legth*12+lease_tm_legth2+lease_tm_legth3/30 as prj_mon_avg_rent, -- 挂牌月平均租金    
    lease_tm_legth+lease_tm_legth2/12+lease_tm_legth3/365 as prj_year_avg_rent, --挂牌年平均租金
    lease_tm_legth + lease_tm_legth2 / 12 + lease_tm_legth3 / 365 AS lease_tm_y, -- 总年数 租赁期（年）
    lease_tm_legth * 12 + lease_tm_legth2 + lease_tm_legth3 / 30 AS lease_tm_m, -- 总月数 租赁期（月）   
    lease_tm_legth * 365 + lease_tm_legth2 * 30 + lease_tm_legth3 AS lease_tm_d, -- 总天数 租赁期（天）  
    lsct_dt,    -- 起租日
    due_dt,     -- 到期日
    orgn_aavg_rent_prc,          -- 原年平均租金价格（元/年）
    aavg_y_rent,               -- 年平均租金
    COALESCE(aavg_y_rent,0) - COALESCE(orgn_aavg_rent_prc,0) as deal_year_avg_rent, -- 年平均租金增值
    (COALESCE(aavg_y_rent,0) - COALESCE(orgn_aavg_rent_prc,0))/COALESCE(orgn_aavg_rent_prc,0) as init_cntr_prem_rate, -- 较上份合同溢价率
    cntr_sign_dt,              -- 合同签订日期
    cntr_eff_dt,
    deal_rent_prc_rmrk, --成交租金价格备注
    deal_date -- 成交日期
from(select 
    bsn_prj_wrd,      --业务项目关键字
    COALESCE(lease_tm_legth,0)as lease_tm_legth,           -- 年 
    COALESCE(lease_tm_legth2,0)as lease_tm_legth2,          -- 月 
    COALESCE(lease_tm_legth3,0)as lease_tm_legth3,         -- 日
    sevly_valut_cd_dsc, --分别计价代码描述
    deal_rent_prc,            -- 成交租金价格
    deal_rent_prc_unit_cmnt,   -- 成交租金价格单位说明
    lease_area,                -- 出租面积
    deal_rent_tot_prc as deal_total_price,    -- 成交总价（元）
    lsct_dt,    -- 起租日
    due_dt,     -- 到期日
    orgn_aavg_rent_prc,          -- 原年平均租金价格（元/年）
    aavg_y_rent,               -- 年平均租金
    cntr_sign_dt,              -- 合同签订日期
    cntr_eff_dt, -- 合同生效日期
    deal_rent_prc_rmrk, --成交租金价格备注
    deal_date -- 成交日期
from dwd.dwd_ast_lease_deal_rec_fct
where dt = '${dmp_day}' and deal_date is not null 
and mdl_sts = 3
) k1
),
-- 房租基本字段
tzccz_zcczxm_fw as (
select
a.id, -- 房屋资产id
a.bsn_prj_wrd,
a.loct_lo as location, --坐落位置
a.prov_cd_dsc as province, --省份
a.city_cd_dsc as city, --城市
a.regn_cnty_cd_dsc as district, --市辖区
'' as street_house, --街道
a.budt_cd_dsc as biz_district_house, -- 商圈
'' as dt_address_house, --详细地址
M.pl_lo_lgt AS lng_house,                -- 【房屋】经度
M.pl_lo_ltt AS lat_house,                -- 【房屋】纬度
a.cnstrct_area as cnstrct_area, --建筑面积
a.build_use as usage_house, --房屋用途
b.TDSYQXZ as land_right_house, --土地使用权性质
c.ast_cl as asset_class_house, --资产分类
a.owner_nm as owner_house, --产权人
a.othr_right_sttn_cd, --其他权利情况
-- 为每个房屋创建基本信息MAP结构
MAP(
    'location', COALESCE(a.loct_lo, ''),
    'province', COALESCE(a.prov_cd_dsc, ''),
    'city', COALESCE(a.city_cd_dsc, ''),
    'district', COALESCE(a.regn_cnty_cd_dsc, ''),
    'street', COALESCE('', ''),
    'biz_district', COALESCE(a.budt_cd_dsc, ''),
    'dt_address', COALESCE('', ''),
    'lng', COALESCE(CAST(M.pl_lo_lgt AS STRING), ''),
    'lat', COALESCE(CAST(M.pl_lo_ltt AS STRING), ''),
    'area', COALESCE(CAST(a.cnstrct_area AS STRING), ''),
    'usage', COALESCE(a.build_use, ''),
    'land_right', COALESCE(b.TDSYQXZ, ''),
    'asset_class', COALESCE(c.ast_cl, ''),
    'owner', COALESCE(a.owner_nm, ''),
    'other_right', COALESCE(a.othr_right_sttn_cd, '')
) AS house_basic_info_single
from  dim.dim_build_info a
left join ods.ods_bjhl_tzccz_lyxxgl b
on a.build_info_magt_id = b.id
and b.dt = '${dmp_day}'
left join std.std_bjhl_tzccz_zcczxm_fw_d c
on a.id = 'BJHL'||c.id||'FW'
and c.dt = '${dmp_day}'
LEFT JOIN dim.dim_build_obj_info M
on A.bsn_prj_wrd = 'BJHL' || M.prj_tbl_id || 'ZCCZ'
and M.dt = '${dmp_day}'
where A.dt = '${dmp_day}'
and A.edw_end_dt = '20991231'
 ),

-- 房屋编号中间表
tzccz_zcczxm_fw_numbered as (
    select
        *,
        ROW_NUMBER() OVER (PARTITION BY bsn_prj_wrd ORDER BY id) as house_num
    from tzccz_zcczxm_fw
),
--房屋统计字段
tzccz_zcczxm_fw_2 as (
    select
        bsn_prj_wrd,
        -- 数量统计字段
        COUNT(id) AS location_cnt_house,                                        -- 【房屋】坐落位置数量（非去重计数，计算房屋资产id数量）
        COUNT(DISTINCT province) AS province_cnt_house,                         -- 【房屋】省份数量（去重计数）
        COUNT(DISTINCT city) AS city_cnt_house,                                 -- 【房屋】城市数量（去重计数）
        COUNT(DISTINCT district) AS district_cnt_house,                         -- 【房屋】市辖区数量（去重计数）
        COUNT(DISTINCT street_house) AS street_cnt_house,                       -- 【房屋】街道数量（去重计数）
        COUNT(DISTINCT biz_district_house) AS biz_district_cnt_house,           -- 【房屋】商圈数量（去重计数）
        COUNT(DISTINCT land_right_house) AS land_right_cnt_house,               -- 【房屋】土地使用权性质数量（去重计数）
        COUNT(DISTINCT asset_class_house) AS asset_class_cnt_house,             -- 【房屋】资产分类数量（去重计数）
        -- 求和字段
        SUM(COALESCE(cnstrct_area, 0)) AS area_sum_house,                       -- 【房屋】总建筑面积（平方米）（求和）
        -- 拼接字段（去重、排序后拼接，使用顿号（、）分隔）
        CONCAT_WS('、',
            COLLECT_SET(
                CASE WHEN province IS NOT NULL AND province != '' THEN province END
            )
        ) AS province_montage_house,                                            -- 【房屋】省份_拼接
        CONCAT_WS('、',
            COLLECT_SET(
                CASE WHEN city IS NOT NULL AND city != '' THEN city END
            )
        ) AS city_montage_house,                                                -- 【房屋】城市_拼接
        CONCAT_WS('、',
            COLLECT_SET(
                CASE WHEN district IS NOT NULL AND district != '' THEN district END
            )
        ) AS district_montage_house,                                            -- 【房屋】市辖区_拼接
        CONCAT_WS('、',
            COLLECT_SET(
                CASE WHEN street_house IS NOT NULL AND street_house != '' THEN street_house END
            )
        ) AS street_montage_house,                                              -- 【房屋】街道_拼接
        CONCAT_WS('、',
            COLLECT_SET(
                CASE WHEN biz_district_house IS NOT NULL AND biz_district_house != '' THEN biz_district_house END
            )
        ) AS biz_district_montage_house,                                        -- 【房屋】商圈_拼接
        CONCAT_WS('、',
            COLLECT_SET(
                CASE WHEN land_right_house IS NOT NULL AND land_right_house != '' THEN land_right_house END
            )
        ) AS land_right_montage_house,                                          -- 【房屋】土地使用权性质_拼接
        CONCAT_WS('、',
            COLLECT_SET(
                CASE WHEN asset_class_house IS NOT NULL AND asset_class_house != '' THEN asset_class_house END
            )
        ) AS asset_class_montage_house,                                          -- 【房屋】资产分类_拼接
        -- 房屋基本信息嵌套MAP结构
        MAP_FROM_ARRAYS(
            COLLECT_LIST(CONCAT('house', COALESCE(CAST(house_num AS STRING), ''))),
            COLLECT_LIST(house_basic_info_single)
        ) AS basic_info_house                                                   -- 【房屋】房屋基本信息
    from (
        select * from tzccz_zcczxm_fw_numbered
        where house_num IS NOT NULL
          AND house_basic_info_single IS NOT NULL
          AND bsn_prj_wrd IS NOT NULL
        order by bsn_prj_wrd, house_num
    )
    GROUP BY bsn_prj_wrd
),

-- 土地
tudi as (
    SELECT
        land_wrd AS land_wrd, -- 土地关键字
        bsn_prj_wrd AS bsn_prj_wrd, -- 业务项目关键字
        land_id AS land_id, -- 土地ID
        loct_lo AS location_land, -- 坐落位置
        land_cerf_no AS land_cerf_no, -- 土地证号
        land_area AS area_land, -- 土地面积
        use AS usage_land, -- 用途
        tp AS type_land, -- 类型
        use_yrs AS usage_period_land, -- 使用年限
        used_yrs AS used_period_land, -- 已用年限
        -- 为每块土地创建基本信息MAP结构
        MAP(
            'location', COALESCE(loct_lo, ''),
            'area', COALESCE(CAST(land_area AS STRING), ''),
            'usage', COALESCE(use, ''),
            'type', COALESCE(tp, ''),
            'usage_period', COALESCE(CAST(use_yrs AS STRING), ''),
            'used_period', COALESCE(CAST(used_yrs AS STRING), '')
        ) AS land_basic_info_single
    FROM dim.dim_land_info -- 土地维
    where dt = '${dmp_day}'
    and edw_end_dt = '20991231'
),
-- 土地编号中间表
tudi_numbered as (
    select
        *,
        ROW_NUMBER() OVER (PARTITION BY bsn_prj_wrd ORDER BY land_id) as land_num
    from tudi
),
tudi_2 as (
    select
        bsn_prj_wrd,
        COUNT(location_land) AS location_cnt_land,  --【土地】坐落位置数量
        SUM(area_land) AS area_sum_land,  --【土地】总土地面积（平方米）
        -- 土地基本信息嵌套MAP结构
        MAP_FROM_ARRAYS(
            COLLECT_LIST(CONCAT('land', COALESCE(CAST(land_num AS STRING), ''))),
            COLLECT_LIST(land_basic_info_single)
        ) AS basic_info_land                                                   -- 【土地】土地基本信息
    FROM (
        select * from tudi_numbered
        where land_num IS NOT NULL
          AND land_basic_info_single IS NOT NULL
          AND bsn_prj_wrd IS NOT NULL
        order by bsn_prj_wrd, land_num
    )
    GROUP BY bsn_prj_wrd
),

-- tzccz_ywdd as (
-- select 
--     prj, 
--     SUM(CASE WHEN py_side_rl_dsc = '出租方' THEN to_acc_amt_yuan ELSE 0 END) AS lessor_txn_serv_fee, -- 出租方交易服务费
--     SUM(CASE WHEN py_side_rl_dsc = '承租方' THEN to_acc_amt_yuan ELSE 0 END) AS rent_txn_serv_fee -- 承租方交易服务费
-- from std.std_bjhl_tzccz_ywdd_d
-- where dt = '${dmp_day}' and ordr_tp_dsc ='交易服务费' and ordr_sts_dsc = '支付成功' and py_side_rl_dsc in ('出租方','承租方')
-- group by prj
-- ),
tzccz_yxczfxx_1 as (
SELECT 
    k1.project_id,
    concat_ws('、', collect_set(cast(k2.usr_nm AS STRING))) AS rent_txn_serv_mem, -- 承租方受托交易服务会员
    concat_ws('、', collect_set(cast(k1.intnt_rent_nm AS STRING))) AS priority_lessee_nm -- 有优先承租权的原承租方名称
FROM (
    SELECT DISTINCT project_id, txn_svc_mbsh AS mbsh_org ,intnt_rent_nm 
    FROM std.std_bjhl_tzccz_yxczfxx_d WHERE dt = '${dmp_day}' AND deposit_sts_dsc = '已交纳' AND txn_svc_mbsh IS NOT NULL
) k1
INNER JOIN (
    SELECT DISTINCT id, usr_nm FROM std.std_bjhl_tuser_d WHERE dt = '${dmp_day}' AND sub_org IS NOT NULL AND usr_nm IS NOT NULL
) k2 ON k1.mbsh_org = k2.id
GROUP BY k1.project_id
),
-- 服务会员对应关系
tbid_fwhydy as (
SELECT 
    k1.project_code,
    concat_ws(',', collect_set(cast(k2.usr_nm AS STRING))) AS lessor_txn_serv_mem -- 出租方受托交易服务会员
FROM (
    SELECT 	
		T1.project_code, -- 项目编号
		T1.txn_svc_mbsh
	FROM std.std_bjhl_tzccz_zcczxm_d T1     --资产出租项目
	WHERE dt = '${dmp_day}' AND T1.txn_svc_mbsh is not null and T1.project_code is not null
) k1
INNER JOIN (
    SELECT DISTINCT id, usr_nm FROM std.std_bjhl_tuser_d WHERE dt = '${dmp_day}'  -- 用户管理
) k2 ON k1.txn_svc_mbsh = k2.id
GROUP BY k1.project_code
),
fbjjmx as (
SELECT  A.id                                                                 AS id -- ID 
       ,A.tzccz_zcczxm_id                                                    AS tzccz_zcczxm_id -- TZCCZ_ZCCZXM_ID        
       ,row_number() over (partition by tzccz_zcczxm_id order by A.id )      AS proj_bid_no -- 项目内出价序号 
       ,B.project_code -- 项目编号 
       ,B.project_name -- 项目名称 
       ,A.lcck                                                               AS lcck -- 楼层/仓库 
       ,A.dj                                                                 AS dj -- 单价 
       ,A.mj                                                                 AS mj -- 面积\t 
       ,A.fbjjxx                                                             AS fbjjxx -- 区域分别计价 
       ,A.fbjjgpfs                                                           AS fbjjgpfs -- 分别计价挂牌价方式 
       ,A.fbjjgpj                                                            AS fbjjgpj -- 分别计价挂牌价 
       ,A.fbjjgpjdw                                                          AS fbjjgpjdw -- 分别计价挂牌价单位 
       ,A.qtfbjjgpjdw                                                        AS qtfbjjgpjdw -- 其他分别计价挂牌价单位 
       ,A.fbjjgpjbz                                                          AS fbjjgpjbz -- 分别计价挂牌价备注 
       ,A.fbjjczmj                                                           AS fbjjczmj -- 分别计价出租面积（平方米） 
    --  case d.CJJDW when 1 then '元/平方米/天' when 2 then '元/月' when 3 then '元/年' when 5 then '元/平方米/月' when 99 then '其他' end
       ,CASE WHEN A.fbjjgpjdw = 1 THEN A.fbjjgpj
             WHEN A.fbjjgpjdw = 5 THEN A.fbjjgpj / 30
             WHEN A.fbjjgpjdw = 2 THEN fbjjgpj / (A.fbjjczmj * 30)
             WHEN A.fbjjgpjdw = 3 THEN fbjjgpj / (A.fbjjczmj * 365) 
          ELSE NULL 
        END                                                                   AS list_price_std_sq_d -- 挂牌价格_标准化（元/平方米/天） 
       ,CASE WHEN A.fbjjgpjdw = 1 THEN A.fbjjgpj
             WHEN A.fbjjgpjdw = 5 THEN A.fbjjgpj / 30
             WHEN A.fbjjgpjdw = 2 THEN fbjjgpj / (A.fbjjczmj * 30)
             WHEN A.fbjjgpjdw = '元/年' THEN fbjjgpj / (A.fbjjczmj * 365) 
          ELSE NULL 
        END * 30                                                               AS list_price_std_sq_m -- 挂牌价格_标准化（元/平方米/月） 
       ,CASE WHEN A.fbjjgpjdw = 1 THEN A.fbjjgpj
             WHEN A.fbjjgpjdw = 5 THEN A.fbjjgpj / 30
             WHEN A.fbjjgpjdw = 2 THEN fbjjgpj / (A.fbjjczmj * 30)
             WHEN A.fbjjgpjdw = 3 THEN fbjjgpj / (A.fbjjczmj * 365) 
          ELSE NULL 
        END * 365                                                              AS list_price_std_sq_y -- 挂牌价格_标准化（元/平方米/年） 
FROM std.std_bjhl_tzccz_zcczxm_fbjjnr A
LEFT JOIN std.std_bjhl_tzccz_zcczxm_d B
ON A.tzccz_zcczxm_id = B.id
AND B.dt = '${dmp_day}'
WHERE A.dt = '${dmp_day}'
),

fbjjmx_concat_group as (
    select
    project_code,
    map_from_arrays(
        array_sort(collect_list(lpad(CAST(proj_bid_no AS STRING), 2, '0') || '_出租面积：' || COALESCE(fbjjczmj, ''))),
        array_sort(collect_list(lpad(CAST(proj_bid_no AS STRING), 2, '0') || '_挂牌价：' || COALESCE(CAST(ROUND(list_price_std_sq_d, 6) AS STRING), '')))
    ) as list_price_sep
    from (
        select * from fbjjmx
        where proj_bid_no IS NOT NULL
          AND fbjjczmj IS NOT NULL
          AND list_price_std_sq_d IS NOT NULL
        order by project_code, proj_bid_no
    )
    group by project_code
),
--分别估价
tzccz_zcczxm_fbgjnr as (
    SELECT
        A.id, -- ID (原始字段: id)
        row_number() over (partition by tzccz_zcczxm_id order by A.id )      AS proj_bid_no, -- 项目内出价序号 
        B.project_code, -- 项目编号 
        B.project_name, -- 项目名称 
        A.lc_warehouse, -- 楼层/仓库 (原始字段: lcck)
        A.unit_price, -- 单价 (原始字段: dj)
        A.area, -- 面积 (原始字段: mj)
        A.regional_pricing, -- 区域分别计价 (原始字段: fbjjxx)
        A.rental_area, -- 分别计价出租面积(平方米) (原始字段: fbjjczmj)
        A.valuation_method, -- 分别计价估价方式 (原始字段: fbjjgpfs)
        A.valuation_unit, -- 分别计价估价单位 (原始字段: fbjjgpjdw)
        A.other_valuation_unit, -- 其他分别计价估价单位 (原始字段: qtfbjjgpjdw)
        A.valuation_remark, -- 分别计价估价备注 (原始字段: fbjjgpjbz)
        A.tzccz_zcczxm_id, -- TZCCZ_ZCCZXM_ID (原始字段: tzccz_zcczxm_id)
        A.valuation_price, -- 分别计价估价价格 (原始字段: fbjjgpj)
        CASE WHEN A.valuation_unit = 1 THEN A.valuation_price
             WHEN A.valuation_unit = 5 THEN A.valuation_price / 30
             WHEN A.valuation_unit = 2 THEN A.valuation_price / (A.rental_area * 30)
             WHEN A.valuation_unit = 3 THEN A.valuation_price / (A.rental_area * 365) 
          ELSE NULL 
        END                                                                   AS appr_price_std_sq_d -- 挂牌价格_标准化（元/平方米/天）明细 
    FROM std.std_bjhl_tzccz_zcczxm_fbgjnr_d A
    LEFT JOIN std.std_bjhl_tzccz_zcczxm_d B
    ON A.tzccz_zcczxm_id = B.id
    AND B.dt = '${dmp_day}'
    WHERE A.dt = '${dmp_day}'
),
--挂牌/估价 标准化
gp_gj_bzh as (
    select 
        A.project_code,
        -- 估价标准化
        case when C.is_resp_val_dsc = '分别计价' then -- 0|统一计价；1|分别计价；2|面议
        B.appr_price_std_sq_d    -- 挂牌价格_标准化（元/平方米/天）明细 
        when C.is_resp_val_dsc = '统一计价' then A.appr_price_std_sq_d
        else '' end 
        as appr_price_std_sq_d, -- 估价价格_标准化（元/平方米/天）
        -- 挂牌价格标准化
        case when C.is_resp_val_dsc = '分别计价' then -- 0|统一计价；1|分别计价；2|面议
        D.list_price_std_sq_d    -- 挂牌价格_标准化（元/平方米/天）明细 
        when C.is_resp_val_dsc = '统一计价' then A.list_price_std_sq_d
        else '' end 
        as list_price_std_sq_d -- 挂牌价格_标准化（元/平方米/天）
        from tzccz_zcczxm A
        LEFT JOIN (
            select project_code, sum(appr_price_std_sq_d * area) / sum(area) as appr_price_std_sq_d 
            from tzccz_zcczxm_fbgjnr 
            group by project_code
        ) B
        ON A.project_code = B.project_code
        LEFT JOIN std.std_bjhl_tzccz_zcczxm_d C
        ON A.project_code = C.project_code 
        AND C.dt = '${dmp_day}' 
        left join (
            SELECT project_code,
            SUM(
                case when prj_prc_unit_dsc = '元/平方米/天' then rent_prj_price
                when prj_prc_unit_dsc = '元/平方米/月' then rent_prj_price * 30
                when prj_prc_unit_dsc = '元/月' then rent_prj_price * 30 / prep_rent_area_sqm
                when prj_prc_unit_dsc = '元/年' then rent_prj_price * 365 / prep_rent_area_sqm
                else 0 end
            )/
            SUM(prep_rent_area_sqm) AS list_price_std_sq_d --分别计价挂牌价格_标准化（元/平方米/天）
            FROM std.std_bjhl_tzccz_zcczxm_d 
            WHERE is_resp_val_dsc = '分别计价'
            GROUP BY project_code
        ) D
        ON A.project_code = D.project_code
),
tzccz_zcczxm_fbgjnr_concat_group as (
    select
        project_code,
        map_from_arrays(
            array_sort(collect_list(lpad(CAST(proj_bid_no AS STRING), 2, '0') || '_出租面积：' || COALESCE(CAST(rental_area AS STRING), '') || '平方米')),
            array_sort(collect_list(lpad(CAST(proj_bid_no AS STRING), 2, '0') || '_挂牌价：' || COALESCE(CAST(ROUND(appr_price_std_sq_d, 6) AS STRING), '') || '元/平方米/天'))
        ) as appr_price_sep
    from (
        select * from tzccz_zcczxm_fbgjnr
        where proj_bid_no IS NOT NULL
          AND rental_area IS NOT NULL
          AND appr_price_std_sq_d IS NOT NULL
        order by project_code, proj_bid_no
    )
    group by project_code
),
pro_info AS (
    SELECT 
        a.project_code, -- 项目编码
        CASE 
            WHEN instr(a.project_code, '-') > 0 
            THEN substr(a.project_code, length(a.project_code) - instr(reverse(a.project_code), '-') + 2)
            ELSE 0 
        END AS prj_num, -- 如果编码中带 '-' 则取0 取重新披露项目编号中“-”后面的数字
        CASE 
            WHEN instr(a.project_code, '-') > 0 
            THEN substr(a.project_code, 1, instr(a.project_code, '-') - 1)
            ELSE a.project_code 
        END AS base_prj_code, -- 基础项目编码（去掉“-”及后面文字）
        lit_star_dt, -- 挂牌开始日期
        lit_end_dt, -- 挂牌结束日期
        CASE 
            WHEN a.prj_prc_unit_dsc = '元/平方米/天' THEN a.rent_lst_prc
            WHEN a.prj_prc_unit_dsc = '元/平方米/月' THEN a.rent_lst_prc / 30
            WHEN a.prj_prc_unit_dsc = '元/月' THEN a.rent_lst_prc / (a.plan_lease_area * 30)
            WHEN a.prj_prc_unit_dsc = '元/年' THEN a.rent_lst_prc / (a.plan_lease_area * 365) 
            ELSE NULL 
        END AS list_price_std_sq_d, -- 挂牌价格_标准化（元/平方米/天）
        CASE 
            WHEN b.hpn_excd_times > 0 THEN '是' 
            ELSE '否' 
        END AS is_extend, -- 是否延牌
        b.crt_tm as prj_entry_date --项目录入日期
    FROM dwd.dwd_ast_lease_prj_fct a
    LEFT JOIN dwd.dwd_prj_fct b
    ON a.project_code = b.prj_id 
    and b.dt = '${dmp_day}' 
    and b.crt_tm is not null
    left join 
     (select bsn_prj_wrd,fnl_ittn_cfrm_tm FROM dwd.dwd_ittn_lhder_fct c where fnl_ittn_cfrm_tm = '是' and dt = '${dmp_day}') c
    on a.bsn_prj_wrd = c.bsn_prj_wrd
    where a.dt = '${dmp_day}'
),
max_prj_num AS (
    -- 计算每个基础项目编码的最大 prj_num
    SELECT 
        base_prj_code,
        MAX(prj_num) AS max_prj_num
    FROM pro_info
    GROUP BY base_prj_code
),
pro_info2 as (
SELECT 
    p.project_code,
    p.prj_num,
    p.base_prj_code, -- 包含基础项目编码
    p.lit_star_dt,
    p.lit_end_dt,
    p.list_price_std_sq_d,
    p.is_extend,
    -- 是否最后一次披露
    CASE 
        WHEN p.prj_num = mpn.max_prj_num THEN '是'
        ELSE '否'
    END AS is_last_list,
    -- 初次挂牌相关字段
    f.lit_star_dt AS f_list_start_date, -- 初次挂牌开始日期
    f.lit_end_dt AS f_list_end_date,   -- 初次挂牌结束日期
    SUBSTR(f.lit_star_dt, 1, 4) AS f_list_start_year, -- 初次挂牌开始年份
    SUBSTR(f.lit_end_dt, 1, 4) AS f_list_end_year,   -- 初次挂牌结束年份
    SUBSTR(f.lit_star_dt, 1, 6) AS f_list_start_ym,  -- 初次挂牌开始年月
    SUBSTR(f.lit_end_dt, 1, 6) AS f_list_end_ym,      -- 初次挂牌结束年月
    -- 初次挂牌价格_标准化（元/平方米/天）
    f.list_price_std_sq_d  AS f_list_price_std_sq_d,
    -- 降价金额_标准化（元/平方米/天）
    p.list_price_std_sq_d - f.list_price_std_sq_d AS reduce_price_std_sq_d
FROM pro_info p
LEFT JOIN max_prj_num mpn
    ON p.base_prj_code = mpn.base_prj_code
LEFT JOIN pro_info f --初次挂牌
    ON p.base_prj_code = f.project_code 
    AND f.prj_num = 0
),
-- 分别计价
cjjl_fbjjnr AS (
    SELECT
        A.id AS id,  -- ID
        B.bsn_prj_wrd,  -- 业务项目关键字
        row_number() over (partition by B.bsn_prj_wrd order by A.id )      AS proj_bid_no, -- 项目内出价序号
        lcck AS lcck,  -- 楼层/仓库
        A.dj AS dj,  -- 单价
        A.mj AS mj,  -- 面积
        A.fbjjxx AS fbjjxx,  -- 区域分别计价
        A.fbjjgpfs AS fbjjgpfs,  -- 分别计价成交租金价格方式
        A.fbjjgpjdw AS fbjjgpjdw,  -- 分别计价成交租金单位
        A.qtfbjjgpjdw AS qtfbjjgpjdw,  -- 其他分别计价成交租金单位
        A.fbjjgpjbz AS fbjjgpjbz,  -- 分别计价成交租金备注
        A.tzccz_cjjl_id AS tzccz_cjjl_id,  -- TZCCZ_CJJL_ID
        A.fbjjgpj AS fbjjgpj,  -- 分别计价成交租金价格
        A.fbjjczmj AS fbjjczmj,  -- 分别计价出租面积（平方米）

        -- 标准化成交价格：统一为元/平方米/天
        CASE 
            WHEN A.fbjjgpjdw = 1 THEN A.fbjjgpj
            WHEN A.fbjjgpjdw = 5 THEN A.fbjjgpj / 30
            WHEN A.fbjjgpjdw = 2 THEN A.fbjjgpj / (fbjjczmj * 30)
            WHEN A.fbjjgpjdw = 3 THEN A.fbjjgpj / (fbjjczmj * 365) 
            ELSE NULL 
        END AS deal_price_std_sq_d  -- 成交价格_标准化（元/平方米/天) 明细
    FROM std.std_bjhl_tzccz_cjjl_fbjjnr A
    INNER JOIN (
        SELECT DISTINCT
            bsn_deal_rec_id AS bsn_deal_rec_id,  -- 业务成交记录ID
            bsn_prj_wrd AS bsn_prj_wrd  -- 业务项目关键字
        FROM dwd.dwd_ast_lease_deal_rec_fct 
        WHERE dt = '${dmp_day}' and mdl_sts = 3
    ) B ON A.tzccz_cjjl_id = B.bsn_deal_rec_id  -- 关联成交记录ID
    where a.dt = '${dmp_day}'
),
cjjl_bzh as (
    select 
        A.project_code,
        case when C.is_resp_val_dsc = '分别计价' then -- 0|统一计价；1|分别计价；2|面议
        B.deal_price_std_sq_d    -- 成交价格_标准化（元/平方米/天) 明细 
        when C.is_resp_val_dsc = '统一计价' then D.deal_rent_metr_unit
        else '' end 
        as deal_price_std_sq_d -- 成交价格_标准化（元/平方米/天) 明细 
        from tzccz_zcczxm A
        LEFT JOIN (
            select bsn_prj_wrd, sum(deal_price_std_sq_d * fbjjczmj) / sum(fbjjczmj) as deal_price_std_sq_d 
            from cjjl_fbjjnr 
            group by bsn_prj_wrd
        ) B
        ON A.bsn_prj_wrd = B.bsn_prj_wrd
        LEFT JOIN std.std_bjhl_tzccz_zcczxm_d C
        ON A.project_code = C.project_code 
        AND C.dt = '${dmp_day}'  
        left join tzccz_cjjl D
        on A.bsn_prj_wrd = D.bsn_prj_wrd
),
cjjl_fbjjnr_concat_group as (
    select
    bsn_prj_wrd,
    map_from_arrays(
        array_sort(collect_list(lpad(CAST(proj_bid_no AS STRING), 2, '0') || '_出租面积：' || COALESCE(CAST(fbjjczmj AS STRING), ''))),
        array_sort(collect_list(lpad(CAST(proj_bid_no AS STRING), 2, '0') || '_挂牌价：' || COALESCE(CAST(ROUND(deal_price_std_sq_d, 6) AS STRING), '')))
    ) as deal_price_sep
    from (
        select * from cjjl_fbjjnr
        where tzccz_cjjl_id IS NOT NULL
          AND fbjjczmj IS NOT NULL
          AND deal_price_std_sq_d IS NOT NULL
        order by bsn_prj_wrd, proj_bid_no
    )
    group by bsn_prj_wrd
),
tzccz_czfxx AS (
    SELECT
    DISTINCT
   rltv_invest AS rltv_invest -- 关联投资人 KHH
  ,lessor_tp AS lessor_tp -- 出租方类型
  ,lessor_tp_dsc AS lessor_tp_dsc -- 出租方类型描述 
  ,CASE WHEN lessor_tp_dsc = '企业资产' AND cast(T2.ast_src_nm AS STRING) = '国资' AND cast(T3.ast_src_nm AS STRING) = '国务院国资委监管' THEN '国务院国资委监管'
        WHEN lessor_tp_dsc = '企业资产' AND cast(T2.ast_src_nm AS STRING) = '国资' AND cast(T3.ast_src_nm AS STRING) = '省级国资委监管' AND cast(T4.ast_src_nm AS STRING) = '北京市国资委' THEN '北京市国资委'
        WHEN lessor_tp_dsc = '企业资产' AND cast(T2.ast_src_nm AS STRING) = '国资' AND cast(T3.ast_src_nm AS STRING) != '国务院国资委监管' AND cast(T3.ast_src_nm AS STRING) NOT IN ('中央其他部委监管','财政部监管') AND cast(T4.ast_src_nm AS STRING) IN ('北京市国资委','北京市') THEN '北京其他部门监管'
        WHEN lessor_tp_dsc = '企业资产' AND cast(T2.ast_src_nm AS STRING) = '国资' AND cast(T3.ast_src_nm AS STRING) != '国务院国资委监管' AND cast(T3.ast_src_nm AS STRING) IN ('中央其他部委监管','财政部监管')  THEN '中央其他部门监管'
        WHEN lessor_tp_dsc = '企业资产' AND cast(T2.ast_src_nm AS STRING) = '国资' AND cast(T3.ast_src_nm AS STRING) != '国务院国资委监管' AND cast(T4.ast_src_nm AS STRING) NOT IN ('北京市国资委','北京市') THEN '其他地区'
        WHEN lessor_tp_dsc = '行政事业' AND cast(T2.ast_src_nm AS STRING) = '市属' THEN '北京市行政事业单位'
        WHEN lessor_tp_dsc = '行政事业' AND cast(T2.ast_src_nm AS STRING) = '中央' THEN '中央行政事业单位'
      ELSE NULL END AS lessor_type_research --出租方类型_研究中心
  
  ,CASE WHEN lessor_tp_dsc = '企业资产' AND cast(T2.ast_src_nm AS STRING) = '国资' AND cast(T3.ast_src_nm AS STRING) = '省级国资委监管' AND cast(T4.ast_src_nm AS STRING) = '北京市国资委' THEN '市管企业'
        WHEN lessor_tp_dsc = '企业资产' AND cast(T2.ast_src_nm AS STRING) = '国资' AND cast(T3.ast_src_nm AS STRING) = '省级国资委监管' AND cast(T4.ast_src_nm AS STRING) != '北京市国资委' THEN '地方国资'
        WHEN lessor_tp_dsc = '企业资产' AND cast(T2.ast_src_nm AS STRING) = '国资' AND cast(T3.ast_src_nm AS STRING) = '省级其他部门监管' AND cast(T4.ast_src_nm AS STRING) = '北京市' THEN '市管企业'
        WHEN lessor_tp_dsc = '企业资产' AND cast(T2.ast_src_nm AS STRING) = '国资' AND cast(T3.ast_src_nm AS STRING) = '省级其他部门监管' AND cast(T4.ast_src_nm AS STRING) != '北京市' THEN '地方国资'
        WHEN lessor_tp_dsc = '企业资产' AND cast(T2.ast_src_nm AS STRING) = '国资' AND cast(T3.ast_src_nm AS STRING) = '市级国资委监管' AND cast(T4.ast_src_nm AS STRING) = '北京市' THEN '区管企业'
      ELSE NULL END AS	lessor_type_municipal --出租方类型_市属
  ,project_type AS project_type -- 项目类型
  ,project_type_dsc AS project_type_dsc -- 项目类型描述
  ,k1.lessor_nm AS lessor_nm -- 出租方名称 
  ,prov AS prov -- 出租方所在省
  ,city AS city -- 出租方所在市
  ,region_county AS region_county -- 出租方所在区
  ,rgst_addr AS rgst_addr -- 出租方注册地
  -- GL2020BJ1001222、GL2020BJ1001222-2的出租方名称在源表中已经拼接，出租方信息在此处手动拼接，否则会导致项目数据出现重复。
  ,case when project_id in (4448,5033) then '科学研究和技术服务业,租赁和商务服务业'
        else industry_tp_dsc end AS industry_tp_dsc -- 所属行业类型描述 
  ,industry -- 所属行业 
  ,case when project_id in (4448,5033) then '专业技术服务业,商务服务业'
        else industry_dsc end  AS industry_dsc -- 所属行业描述 
  ,ecn_char_dsc AS ecn_char_dsc -- 经济性质描述 
  ,blng_grp AS blng_grp -- 所属集团 
  ,project_id AS project_id -- 项目ID
  ,pre_esr_prj_id AS pre_esr_prj_id -- 预披露项目ID
  ,cast(T2.ast_src_nm AS STRING) AS ast_src_2_cla -- 资产来源(2级)
  ,cast(T3.ast_src_nm AS STRING) AS ast_src_3_cla -- 资产来源(3级)
  ,cast(T4.ast_src_nm AS STRING) AS ast_src_4_cla -- 资产来源(4级)
  ,cast(T5.ast_src_nm AS STRING) AS ast_src_5_cla -- 资产来源(5级)
  ,entp_hier_num AS entp_hier_num -- 企业层级数量 
  ,entp_hier_num_dsc AS entp_hier_num_dsc -- 企业层级数量描述 
  ,cast(k1.lessor_nm AS STRING) AS ast_src_entp_1_cla -- 资产来源（企业1级）
  ,cast(k2.lessor_nm AS STRING) AS ast_src_entp_2_cla -- 资产来源（企业2级）
  ,cast(k3.lessor_nm AS STRING) AS ast_src_entp_3_cla -- 资产来源（企业3级）
  ,cast(k4.lessor_nm AS STRING) AS ast_src_entp_4_cla -- 资产来源（企业4级）
  ,'' AS ast_src_entp_5_cla -- 资产来源（企业5级） 保留字段，暂时置空
  -- GL2020BJ1001222、GL2020BJ1001222-2的出租方名称在源表中已经拼接，出租方信息在此处手动拼接，否则会导致项目数据出现重复。
  ,case when project_id in (4448,5033) then '"中国汽车技术研究中心有限公司":"复印；办公用品销售；日用品销售；日用家电零售；服装服饰零售；化妆品零售；鞋帽零售；金属工具销售；金属制品销售；电工器材销售；安防设备销售；电气设备销售；厨具卫具及日用杂品批发；厨具卫具及日用杂品零售；日用产品修理；通用设备修理；日用电器修理；仪器仪表修理；计算机及办公设备维修；家具安装和维修服务；电气设备修理；通讯设备修理。（除依法须经批准的项目外，凭营业执照依法自主开展经营活动）。许可项目：住宿服务；餐饮服务；食品经营（销售预包装食品）；食品互联网销售（销售预包装食品）；烟草制品零售；城市生活垃圾经营性服务。（依法须经批准的项目，经相关部门批准后方可开展经营活动，具体经营项目以相关部门批准文件或许可证件为准）。技术开发、转让、培训、咨询及服务；汽车及摩托车产品及设备的开发、设计、研制、检测；汽车行业的信息服务；因特网信息服务；自营和代理各类商品和技术的进出口（国家限定公司经营或禁止进出口的商品和技术除外）；软件开发、销售；展览展示服务；限分支机构经营：期刊的出版发行（以上经营范围涉及行业许可的凭许可证件，在有效期内经营，国家有专项专营规定的按规定办理）（依法须经批准的项目，经相关部门批准后方可开展经营活动）","中汽研企业管理服务（天津）有限公司":"一般项目：单位后勤管理服务；物业管理；专业保洁、清洗、消毒服务；互联网销售（除销售需要许可的商品）；土地使用权租赁；住房租赁；非居住房地产租赁；摄像及视频制作服务；摄影扩印服务；平面设计；广告设计、代理；图文设计制作；广告制作；专业设计服务；旅客票务代理；会议及展览服务；婚庆礼仪服务；礼仪服务；计算机及通讯设备租赁；办公设备租赁服务；建筑工程机械与设备租赁；运输设备租赁服务；日用品出租；特种设备出租；医疗设备租赁；汽车租赁；停车场服务；洗染服务；外卖递送服务；居民日常生活服务；运输货物打包服务；办公服务；打字复印；办公用品销售；日用品销售；日用家电零售；服装服饰零售；化妆品零售；鞋帽零售；金属工具销售；金属制品销售；电工器材销售；安防设备销售；电气设备销售；厨具卫具及日用杂品批发；厨具卫具及日用杂品零售；日用产品修理；通用设备修理；日用电器修理；仪器仪表修理；计算机及办公设备维修；家具安装和维修服务；电气设备修理；通讯设备修理。（除依法须经批准的项目外，凭营业执照依法自主开展经营活动）。许可项目：住宿服务；餐饮服务；食品经营（销售预包装食品）；食品互联网销售（销售预包装食品）；烟草制品零售；城市生活垃圾经营性服务。（依法须经批准的项目，经相关部门批准后方可开展经营活动，具体经营项目以相关部门批准文件或许可证件为准）"' 
    else kh.business_scope end as  business_scope -- 经营范围！
FROM std.std_bjhl_tzccz_czfxx_d T1
LEFT JOIN (SELECT ast_src_cd, ast_src_nm FROM std.std_bjhl_tbjhl_zcly_d WHERE dt='${dmp_day}' AND lvl='1') T2 ON T2.ast_src_cd = T1.ast_src_2_cla
LEFT JOIN (SELECT ast_src_cd, ast_src_nm FROM std.std_bjhl_tbjhl_zcly_d WHERE dt='${dmp_day}' AND lvl='2') T3 ON T3.ast_src_cd = T1.ast_src_3_cla
LEFT JOIN (SELECT ast_src_cd, ast_src_nm FROM std.std_bjhl_tbjhl_zcly_d WHERE dt='${dmp_day}' AND lvl='3') T4 ON T4.ast_src_cd = T1.ast_src_4_cla
LEFT JOIN (SELECT ast_src_cd, ast_src_nm FROM std.std_bjhl_tbjhl_zcly_d WHERE dt='${dmp_day}' AND lvl='4') T5 ON T5.ast_src_cd = T1.ast_src_5_cla
LEFT JOIN (
    SELECT id, lessor_nm FROM std.std_bjhl_tzccz_czfxx_d k1 WHERE dt = '${dmp_day}' and rltv_invest is not null
) k1 ON T1.ast_src_entp_1_cla = k1.id
LEFT JOIN (
    SELECT id, lessor_nm FROM std.std_bjhl_tzccz_czfxx_d k2 WHERE dt = '${dmp_day}' and rltv_invest is not null
) k2 ON T1.ast_src_entp_2_cla = k2.id
LEFT JOIN (
    SELECT id, lessor_nm FROM std.std_bjhl_tzccz_czfxx_d k3 WHERE dt = '${dmp_day}' and rltv_invest is not null
) k3 ON T1.ast_src_entp_3_cla = k3.id
LEFT JOIN (
    SELECT id, lessor_nm FROM std.std_bjhl_tzccz_czfxx_d k4 WHERE dt = '${dmp_day}' and rltv_invest is not null
) k4 ON T1.ast_src_entp_4_cla = k4.id
-- LEFT JOIN (
--     SELECT id, lessor_nm FROM std.std_bjhl_tzccz_czfxx_d k4 WHERE dt = '${dmp_day}'
-- ) k5 ON T1.ast_src_entp_5_cla = k5.id
LEFT JOIN (SELECT optr_nm,cust_no,business_scope
	   FROM std.std_bjhl_tjgkhxx_d	   --机构客户信息表
	   WHERE DT = '${dmp_day}' ) kh 
ON T1.rltv_invest = kh.cust_no
WHERE T1.dt = '${dmp_day}' AND T1.project_id IS NOT NULL 
and T1.rltv_invest is not null
),
tzccz_yxczfxx_2 as (
    select
     a.project_id
    ,a.id as intnt_rent_id -- 意向承租方ID
    ,c1.reg_intend_lessee_cnt
    ,c2.mm_intend_lessee_cnt
    ,c3.bid_intend_lessee_cnt
    ,a.intnt_rent_nm AS intnt_rent_nm -- 意向承租方名称
    ,a.intnt_rent_tp_dsc AS intnt_rent_tp_dsc -- 意向承租方类型描述
    ,a.prov AS prov -- 省
    ,a.city AS city -- 市
    ,a.region_county AS region_county -- 区(县)
    ,b.second_place_bidder as lessee_name_second_bid --竞价排名第二的意向承租方名称
    ,b.second_place_amount as lessee_offer_second_bid --竞价排名第二的意向承租方报价（元/平方米/天）
    ,b.third_place_bidder as lessee_name_third_bid --竞价排名第三的意向承租方名称
    ,b.third_place_amount as lessee_offer_third_bid --竞价排名第三的意向承租方报价（元/平方米/天）
    ,b.bid_unit, -- 意向承租方报价单位
from std.std_bjhl_tzccz_yxczfxx_d a
left join (
    select project_id,count(distinct intnt_rent_nm) as reg_intend_lessee_cnt 
        from std.std_bjhl_tzccz_yxczfxx_d where dt = '${dmp_day}' 
        group by project_id
) c1
on a.project_id = c1.project_id
left join (
    select project_id,count(distinct intnt_rent_nm) as mm_intend_lessee_cnt 
    from std.std_bjhl_tzccz_yxczfxx_d where dt = '${dmp_day}' 
    and deposit_sts_dsc = '已交纳' 
    group by project_id
) c2
on a.project_id = c2.project_id
left join (
    select project_id,count(distinct intnt_rent_nm) as bid_intend_lessee_cnt 
    from std.std_bjhl_tzccz_yxczfxx_d where dt = '${dmp_day}' 
    and final_qlfy_rst_dsc = '获得资格'
    group by project_id
) c3
on a.project_id = c3.project_id
left join (
    SELECT
    project_id,
    bid_unit, -- 意向承租方报价单位  
    MAX(CASE WHEN bid_rank = 2 THEN intnt_rent_nm END) AS second_place_bidder, -- 竞价排名第二的意向承租方名称
    MAX(CASE WHEN bid_rank = 2 THEN bid_amount END) AS second_place_amount, -- 竞价排名第二的报价金额
    MAX(CASE WHEN bid_rank = 3 THEN intnt_rent_nm END) AS third_place_bidder, -- 竞价排名第三的意向承租方名称
    MAX(CASE WHEN bid_rank = 3 THEN bid_amount END) AS third_place_amount -- 竞价排名第三的报价金额
FROM (
        SELECT 
            yxczf.project_id,
            khxx.full_nm AS intnt_rent_nm, -- 意向承租方名称
            wljj_mx.bjje AS bid_amount, -- 竞价金额
            wljj.jgxsdw ||COALESCE(wljj.czdjdw,'') AS bid_unit, -- 意向承租方报价单位
            ROW_NUMBER() OVER (
                PARTITION BY yxczf.project_id
                ORDER BY wljj_mx.BJJE DESC
            ) AS bid_rank -- 按竞价金额从高到低排名
        FROM std.std_bjhl_tzccz_yxczfxx_d yxczf
        LEFT JOIN ods.ods_bjhl_tzccz_tzccz_wljj wljj -- 网络竞价
            ON yxczf.id = wljj.tzccz_wljj_id
            AND wljj.dt = '${dmp_day}'
        LEFT JOIN ods.ods_bjhl_tzccz_tzccz_wljj_mx wljj_mx -- 网络竞价明细
            ON yxczf.id = wljj_mx.tzccz_wljj_id
            AND wljj_mx.dt = '${dmp_day}'
        INNER JOIN ods.ods_bjhl_tzccz_tbid_jjcc wljj_jjcc
            ON wljj.id = wljj_jjcc.tzccz_wljj_id
            AND wljj_jjcc.dt = '${dmp_day}'
            AND wljj_jjcc.jjzt = 400 -- 竞价状态(限制)
        LEFT JOIN std.std_bjhl_tkhxx_d khxx -- 客户信息
            ON  yxczf.rltv_invest = khxx.cust_no 
            AND khxx.dt = '${dmp_day}'
        WHERE yxczf.dt = '${dmp_day}'
            AND yxczf.project_id IS NOT NULL
            AND ddb.trade_price IS NOT NULL
    ) ranked_bidders
    WHERE bid_rank IN (2, 3) -- 只关注第二和第三名
    GROUP BY project_id,bid_unit
) b
on a.project_id = b.project_id
where a.dt = '${dmp_day}' 
and a.project_id is not null
),
--最终承租方
zzczfxx as 
(
    SELECT 
        'BJHL'||t1.PRJ||'ZCCZ' AS BSN_PRJ_WRD, --业务项目关键字
        t1.rent, -- 最终承租方ID
        k2.INTNT_RENT_NM AS intnt_rent_nm, -- 最终承租方 
        k2.intnt_rent_tp_dsc AS intnt_rent_tp_dsc, -- 意向承租方类型描述 
        k2.fnl_qua_cfm_dt AS ulti_lessee_confirm_date,  -- 确认最终承租方日期
        k2.prov AS prov, -- 省
        k2.city AS city, -- 市
        k2.region_county AS region_county -- 区(县)
    FROM
        STD.STD_BJHL_TZCCZ_CJJL_D T1 --成交记录
    LEFT JOIN 
    (
        SELECT distinct 
        ID, --ID 
        INTNT_RENT_NM, -- 意向承租方名称
        prov AS prov, -- 省
        city AS city, -- 市
        region_county AS region_county, -- 区(县)
        intnt_rent_tp_dsc,
        fnl_qua_cfm_dt  -- 确认最终承租方日期
        FROM
        STD.STD_BJHL_TZCCZ_YXCZFXX_D --意向承租方信息
        WHERE
        dt='${dmp_day}' 
    )k2 ON t1.rent=k2.id
    WHERE
    t1.dt='${dmp_day}'
    and t1.mdl_sts = 3
),
-- 机械设备聚合
jxsb_agg as (
    SELECT
        tzccz_zcczxm_id as project_id,
        CONCAT_WS('、', COLLECT_LIST(DISTINCT CASE WHEN mc IS NOT NULL THEN mc END)) AS machine_nm
    FROM ods.ods_bjhl_tzccz_zcczxm_jxsb
    WHERE dt = '${dmp_day}'
    GROUP BY tzccz_zcczxm_id
),
-- 交通运输工具聚合
jtgj_agg as (
    SELECT
        tzccz_zcczxm_id as project_id,
        CASE WHEN SUM(CASE WHEN sffxq = '1' THEN 1 ELSE 0 END) > 0 THEN '是' ELSE '否' END AS is_aircraft, -- 是否有飞行器
        SUM(CASE WHEN sl IS NOT NULL AND sl != '' THEN CAST(sl AS INT) ELSE 0 END) AS vehicle_num
    FROM ods.ods_bjhl_tzccz_zcczxm_jtgj
    WHERE dt = '${dmp_day}'
    GROUP BY tzccz_zcczxm_id
),
-- 其他资产聚合
qt_agg as (
    SELECT
        tzccz_zcczxm_id as project_id,
        CONCAT_WS('、', COLLECT_LIST(DISTINCT CASE WHEN mc IS NOT NULL THEN mc END)) AS other_asset_nm
    FROM ods.ods_bjhl_tzccz_zcczxm_qt
    WHERE dt = '${dmp_day}'
    GROUP BY tzccz_zcczxm_id
),
-- 北交所收入
bjssr as (
SELECT  
        b.project_code as project_code
      --  ,(case a.fyjs WHEN 1 THEN '出租方交易服务会员' WHEN 2 THEN '承租方交易服务会员' WHEN 3 THEN '北交所' end) fyjs
       ,sum(case when a.cc_rl = 1 then a.cc_amt_yuan else 0 end) as lessor_memb_comm -- 出租方会员分佣金额（元）
       ,sum(case when a.cc_rl = 2 then a.cc_amt_yuan else 0 end) as lessee_memb_comm -- 承租方会员分佣金额（元）
       ,'' as intend_lessee_mmb_comm --意向承租方会员分佣金额（元）置空
FROM std.std_bjhl_tzccz_fyjl_fymx_d a
inner join std.std_bjhl_tzccz_zcczxm_d b
on a.prj = b.id
AND b.project_code not LIKE 'GL0%'
AND b.project_code not LIKE 'TL0%'
AND a.dt =b.dt
where  a.dt = '${dmp_day}'
group by b.project_code
),
jyfwf as (
select 
    'BJHL'||prj||'ZCCZ' as bsn_prj_wrd, 
    SUM(CASE WHEN py_side_rl_dsc = '出租方' THEN to_acc_amt_yuan ELSE 0 END) AS lessor_service_fee, -- 出租方交易服务费
    SUM(CASE WHEN py_side_rl_dsc = '承租方' THEN to_acc_amt_yuan ELSE 0 END) AS lessee_service_fee, -- 承租方交易服务费
    SUM(CASE WHEN py_side_rl_dsc = '出租方' THEN to_acc_amt_yuan ELSE 0 END) + SUM(CASE WHEN py_side_rl_dsc = '承租方' THEN to_acc_amt_yuan ELSE 0 END) as total_revenue --北交所总收入（元）
from std.std_bjhl_tzccz_ywdd_d
where dt = '${dmp_day}' and ordr_tp_dsc ='交易服务费' and ordr_sts_dsc = '支付成功' and py_side_rl_dsc in ('出租方','承租方')
group by prj
),
-- 主查询部分1：不包含MAP字段的数据，进行去重
main_query_no_map as (
SELECT DISTINCT
    A.project_code AS project_id,                                   -- 项目编号
    A.project_name AS project_name,                                 -- 项目名称
    A.ast_cgy_dsc AS asset_cate,                                    -- 资产类别
    A.prj_sts_dsc AS project_status,                                -- 项目当前状态_系统值
    CASE WHEN date_format(CURRENT_DATE, 'yyyyMMdd')  BETWEEN A.esr_strt_dt AND A.esr_end_dt OR A.prj_sts_dsc = '已披露' 
        THEN '是' ELSE '否' END             AS is_listing,                                    -- 是否披露中
    CASE WHEN E.deal_date IS NOT NULL OR E.deal_date != '' THEN '是' ELSE '否' END AS is_dealed,                                     -- 是否已成交
    A.prj_pnp AS prj_manager,                                   -- 项目负责人
    A.prj_blng_dept AS prj_department,                                -- 项目所属部门
    A.prj_clm_dept_nm AS prj_department_claim,                          -- 项目认领部门
    Y.rent_txn_serv_mem AS lessee_member,                                 -- 承租方受托交易服务会员
    Z.lessor_txn_serv_mem AS lessor_member,                                 -- 出租方受托交易服务会员
    A.is_nal_hs_rent AS is_state_lease,                                -- 是否国有房屋出租
    CASE WHEN S.ast_src_3_cla = '省级国资委监管' AND S.ast_src_4_cla = '北京市国资委'
              AND plan_lease_area <= 1000 AND E.deal_year_avg_rent <= 1000000
              THEN '非强制进场'
         WHEN S.ast_src_3_cla = '省级国资委监管' AND S.ast_src_4_cla = '北京市国资委'
              AND plan_lease_area > 1000 AND E.deal_year_avg_rent > 1000000
              THEN '强制进场'
         ELSE NULL END is_mandatory_entry,                            -- 是否强制进场_市属
    case when A.is_exst_prty_rentw = '1' then '是' else '否' end AS is_priority_right,                          -- 是否存在优先承租权
    Y.priority_lessee_nm AS priority_lessee_nm,                            -- 有优先承租权的原承租方名称
    case when B.is_operate = '1' then '是' else '否' end AS is_operate,                              -- 是否运营房源信息
    A.use_rqmt_cd_dsc AS usage_house,                                   -- 【房屋】房屋使用用途
    B.hs_use_crn_sttn_dsc AS usage_state,                                -- 【房屋】使用现状
    case when B.rltv_pre_esr is null then '否' else '是' end AS is_pre_list,                                   -- 是否有预披露项目
    A.deposit_yuan AS margin_money,                                  -- 保证金金额（元）
    A.pcolt_lhder_cnt AS intend_lessee_cnt,                             -- 拟征集承租方个数
    A.pick_mod_dsc AS pick_method,                                   -- 遴选方式
    A.ntw_bid_tp_dsc AS online_bid_type,                               -- 网络竞价类型
    B.is_req_sgn as is_esign,                                      -- 是否需要电签

    --房屋
    C.location_cnt_house AS location_cnt_house,                                        -- 【房屋】坐落位置数量
    C.province_cnt_house AS province_cnt_house,                                        -- 【房屋】省份数量
    C.city_cnt_house AS city_cnt_house,                                            -- 【房屋】城市数量
    C.district_cnt_house AS district_cnt_house,                                        -- 【房屋】市辖区数量
    C.street_cnt_house AS street_cnt_house,                                          -- 【房屋】街道数量
    C.biz_district_cnt_house AS biz_district_cnt_house,                                    -- 【房屋】商圈数量
    C.area_sum_house AS area_sum_house,                                            -- 【房屋】总建筑面积（平方米）
    C.land_right_cnt_house AS land_right_cnt_house,                                      -- 【房屋】土地使用权性质数量
    C.asset_class_cnt_house AS asset_class_cnt_house,                                     -- 【房屋】资产分类数量
    C.province_montage_house AS province_montage_house,                                    -- 【房屋】省份_拼接
    C.city_montage_house AS city_montage_house,                                        -- 【房屋】城市_拼接
    C.district_montage_house AS district_montage_house,                                    -- 【房屋】市辖区_拼接
    C.street_montage_house AS street_montage_house,                                      -- 【房屋】街道_拼接
    C.biz_district_montage_house AS biz_district_montage_house,                                -- 【房屋】商圈_拼接
    C.land_right_montage_house AS land_right_montage_house,                                  -- 【房屋】土地使用权性质_拼接
    C.asset_class_montage_house AS asset_class_montage_house,                                 -- 【房屋】资产分类_拼接

    -- 土地
    case when F.bsn_prj_wrd is null then '否' else '是' end AS is_land, -- 【土地】是否有土地
    F.location_cnt_land AS location_cnt_land,  --【土地】坐落位置数量
    F.area_sum_land AS area_sum_land,  --【土地】总土地面积（平方米）

    -- 其他资产
    COALESCE(G_AGG.project_id, '否') AS is_machine,                                    -- 【机械设备】是否有机械设备
    G_AGG.machine_nm AS machine_nm,                                    -- 【机械设备】机械设备名称_拼接
    COALESCE(H_AGG.project_id, '否') AS is_vehicle,                                    -- 【交通运输工具】是否有交通运输工具
    COALESCE(H_AGG.is_aircraft, '否') AS is_aircraft,                                   -- 【交通运输工具】是否有飞行器
    COALESCE(H_AGG.vehicle_num, 0) AS vehicle_num,                                   -- 【交通运输工具】交通运输工具数量
    COALESCE(I_AGG.project_id, '否') AS is_other_asset,                                -- 【其他资产】是否有其他资产
    I_AGG.other_asset_nm AS other_asset_nm,                                -- 【其他资产】其他资产名称_拼接

    -- 日期和价格相关字段
    CASE
        WHEN LENGTH(A.esr_strt_dt) = 8 THEN CONCAT(SUBSTR(A.esr_strt_dt, 1, 4), '-', SUBSTR(A.esr_strt_dt, 5, 2), '-', SUBSTR(A.esr_strt_dt, 7, 2))
        ELSE A.esr_strt_dt
    END as list_start_date,                               -- 当前挂牌开始日期
    CASE
        WHEN LENGTH(A.esr_end_dt) = 8 THEN CONCAT(SUBSTR(A.esr_end_dt, 1, 4), '-', SUBSTR(A.esr_end_dt, 5, 2), '-', SUBSTR(A.esr_end_dt, 7, 2))
        ELSE A.esr_end_dt
    END as list_end_date,                                 -- 当前挂牌结束日期
    SUBSTR(A.esr_strt_dt, 1, 4) AS list_start_year,                               -- 当前挂牌开始年份
    SUBSTR(A.esr_end_dt, 1, 4) AS list_end_year,                                 -- 当前挂牌结束年份
    CASE
        WHEN LENGTH(A.esr_strt_dt) = 8 THEN CONCAT(SUBSTR(A.esr_strt_dt, 1, 4), '-', SUBSTR(A.esr_strt_dt, 5, 2))
        WHEN LENGTH(A.esr_strt_dt) = 6 THEN CONCAT(SUBSTR(A.esr_strt_dt, 1, 4), '-', SUBSTR(A.esr_strt_dt, 5, 2))
        ELSE SUBSTR(A.esr_strt_dt, 1, 7)
    END AS list_start_ym,                                 -- 当前挂牌开始年月
    CASE
        WHEN LENGTH(A.esr_end_dt) = 8 THEN CONCAT(SUBSTR(A.esr_end_dt, 1, 4), '-', SUBSTR(A.esr_end_dt, 5, 2))
        WHEN LENGTH(A.esr_end_dt) = 6 THEN CONCAT(SUBSTR(A.esr_end_dt, 1, 4), '-', SUBSTR(A.esr_end_dt, 5, 2))
        ELSE SUBSTR(A.esr_end_dt, 1, 7)
    END AS list_end_ym,                                   -- 当前挂牌结束年月
    365 AS annual_days,                                   -- 标准化年天数（天）
    30 AS monthly_days,                                  -- 标准化月天数（天）
    B.is_resp_val_dsc AS is_sep_price,                                  -- 是否分别计价
    A.rent_lst_prc AS list_price_uni,                                -- 挂牌价格_统一计价录入值
    A.prj_prc_unit_dsc AS list_price_unit_uni,                           -- 挂牌价格单位_统一计价录入值
    B.othr_unit AS list_price_other_unit_uni,                            -- 挂牌价格_其他单位录入值
    A1.list_price_std_sq_d                                     AS list_price_std_sq_d,                           -- 挂牌价格_标准化（元/平方米/天）
    A1.list_price_std_sq_d * 30                                AS list_price_std_sq_m,                           -- 挂牌价格_标准化（元/平方米/月）
    A1.list_price_std_sq_d * 365                               AS list_price_std_sq_y,                           -- 挂牌价格_标准化（元/平方米/年）
    A1.list_price_std_sq_d      * A.plan_lease_area            AS list_price_std_d,                              -- 挂牌价格_标准化（元/天）
    A1.list_price_std_sq_d * 30 * A.plan_lease_area            AS list_price_std_m,                              -- 挂牌价格_标准化（元/月）
    A1.list_price_std_sq_d * 365 * A.plan_lease_area           AS list_price_std_y,                              -- 挂牌价格_标准化（元/年）
    B.avrg_mo_rnt_fee_yuan_mo as list_price_sys_m, -- 平均月租金_系统值（元/月）
    B.avrg_yr_rnt_fee_yuan_yr as list_price_sys_y, -- 平均年租金_系统值（元/年）
    B.avrg_yr_rnt_fee_yuan_yr / 365 as list_price_sys_d, -- 平均日租金_系统计算值（元/天）

    -- 平均日租金_系统计算值（元/平方米/天）：平均年租金_系统值（元/年）/（拟出租面积*365）
    CASE
        WHEN A.plan_lease_area > 0 THEN B.avrg_yr_rnt_fee_yuan_yr / (A.plan_lease_area * 365)
        ELSE NULL
    END AS list_price_sys_std_sq_d,

    -- 平均月租金_系统计算值（元/平方米/月）：平均月租金_系统值（元/月）/拟出租面积
    CASE
        WHEN A.plan_lease_area > 0 THEN B.avrg_mo_rnt_fee_yuan_mo / A.plan_lease_area
        ELSE NULL
    END AS list_price_sys_std_sq_m,

    -- 平均年租金_系统计算值（元/平方米/年）：平均年租金_系统值（元/年）/拟出租面积
    CASE
        WHEN A.plan_lease_area > 0 THEN B.avrg_yr_rnt_fee_yuan_yr / A.plan_lease_area
        ELSE NULL
    END AS list_price_sys_std_sq_y,
    A.plan_lease_area as lease_area,                                    -- 拟出租面积（平方米）
    A.lestm_tp_cd_dsc AS lease_prd_type,                                -- 租赁期类型
    A.rng_tp_cd_dsc AS lease_prd_range_type,                          -- 租赁期区间类型

    -- 挂牌租赁期_录入值1：如果租赁期是区间，则取"最少"值；如果租赁期是固定值，则取"租赁期时长"
    CASE
        WHEN A.lestm_tp_cd_dsc = '区间' THEN
            CONCAT(
                COALESCE(A.least, ''), '年',
                COALESCE(A.least_2, ''), '月',
                COALESCE(A.least_3, ''), '日'
            )
        WHEN A.lestm_tp_cd_dsc = '固定值' THEN
            CONCAT(
                COALESCE(A.lease_tm_legth, ''), '年',
                COALESCE(A.lease_tm_legth_2, ''), '月',
                COALESCE(A.lease_tm_legth_3, ''), '日'
            )
        ELSE NULL
    END AS lease_prd_ent1,                                -- 挂牌租赁期_录入值1

    -- 挂牌租赁期_录入值2：如果租赁期是区间，则取"最多"值；如果租赁期是固定值，则取空值
    CASE
        WHEN A.lestm_tp_cd_dsc = '区间' THEN
            CONCAT(
                COALESCE(A.most, ''), '年',
                COALESCE(A.most_2, ''), '月',
                COALESCE(A.most_3, ''), '日'
            )
        WHEN A.lestm_tp_cd_dsc = '固定值' THEN NULL
        ELSE NULL
    END AS lease_prd_ent2,                                -- 挂牌租赁期_录入值2
    CASE
        WHEN LENGTH(A.expi_dt) = 8 THEN CONCAT(SUBSTR(A.expi_dt, 1, 4), '-', SUBSTR(A.expi_dt, 5, 2), '-', SUBSTR(A.expi_dt, 7, 2))
        ELSE A.expi_dt
    END as lease_prd_end_date,                            -- 挂牌租赁期_截止日期
    A.lease_prd_std_d as lease_prd_std_d,                               -- 挂牌租赁期_标准化（天）
    A.lease_prd_std_d /30 as lease_prd_std_m,                               -- 挂牌租赁期_标准化（月）
    A.lease_prd_std_d /365 as lease_prd_std_y,                               -- 挂牌租赁期_标准化（年）
    A1.list_price_std_sq_d * (A.lease_prd_std_d - A.free_prd_std_d)  * A.plan_lease_area  AS list_total_price_cal,        -- 挂牌总价_计算值(元)
    A.rent_lit_tot_prc AS list_total_price_sys,  -- 挂牌总价_系统计算值(元)
    B.is_resp_val AS is_sep_appraise,                               -- 是否分别估价
    A.rnt_fee_est_yuan AS appr_price_uni,                                -- 租金估价_统一估价录入值
    A.est_unit_dsc AS appr_price_unit_uni,                           -- 估价单位_统一估价录入值
    A1.appr_price_std_sq_d AS appr_price_std_sq_d,                      -- 租金估价_标准化（元/平方米/天）
    -- 租金估价_总价(元) =  租金估价_标准化（元/平方米/天）*（挂牌租赁期_标准化（天）-免租期_标准化（天））*拟出租面积（平方米）
    A1.appr_price_std_sq_d * (A.lease_prd_std_d - A.free_prd_std_d) * A.plan_lease_area AS appr_total_price,       -- 租金估价_总价(元)
    case when instr(A.project_code, '-') > 0
            then substr(A.project_code, length(A.project_code) - instr(reverse(A.project_code), '-') + 2)
            else substr(A.project_code,-1) end AS re_list_num,      -- 重新披露次序
    P.is_last_list AS is_last_list,                                  -- 是否最后一次披露
    CASE
        WHEN LENGTH(P.f_list_start_date) = 8 THEN CONCAT(SUBSTR(P.f_list_start_date, 1, 4), '-', SUBSTR(P.f_list_start_date, 5, 2), '-', SUBSTR(P.f_list_start_date, 7, 2))
        ELSE P.f_list_start_date
    END AS f_list_start_date,                             -- 初次挂牌开始日期
    CASE
        WHEN LENGTH(P.f_list_end_date) = 8 THEN CONCAT(SUBSTR(P.f_list_end_date, 1, 4), '-', SUBSTR(P.f_list_end_date, 5, 2), '-', SUBSTR(P.f_list_end_date, 7, 2))
        ELSE P.f_list_end_date
    END AS f_list_end_date,                                 -- 初次挂牌结束日期
    P.f_list_start_year AS f_list_start_year,                             -- 初次挂牌开始年份
    P.f_list_end_year AS f_list_end_year,                               -- 初次挂牌结束年份
    CASE
        WHEN LENGTH(P.f_list_start_ym) = 6 THEN CONCAT(SUBSTR(P.f_list_start_ym, 1, 4), '-', SUBSTR(P.f_list_start_ym, 5, 2))
        ELSE P.f_list_start_ym
    END AS f_list_start_ym,                               -- 初次挂牌开始年月
    CASE
        WHEN LENGTH(P.f_list_end_ym) = 6 THEN CONCAT(SUBSTR(P.f_list_end_ym, 1, 4), '-', SUBSTR(P.f_list_end_ym, 5, 2))
        ELSE P.f_list_end_ym
    END AS f_list_end_ym,                                 -- 初次挂牌结束年月
    P.f_list_price_std_sq_d AS f_list_price_std_sq_d,                         -- 初次挂牌价格_标准化（元/平方米/天）
    P.reduce_price_std_sq_d AS reduce_price_std_sq_d,                         -- 降价金额_标准化（元/平方米/天）
    P.is_extend AS is_extend,                                     -- 是否手工延牌
    A.frtpd_tp_cd_dsc AS free_prd_type,                                 -- 免租期类型
    -- 免租期_录入值1：根据免租期类型进行判断
    CASE
        WHEN A.frtpd_tp_cd_dsc = '区间' THEN
            CONCAT(
                COALESCE(A.frtpd, ''), '年',
                COALESCE(A.frtpd_2, ''), '月',
                COALESCE(A.frtpd_3, ''), '日'
            )
        WHEN A.frtpd_tp_cd_dsc = '固定值' THEN
            CONCAT(
                COALESCE(A.frtpd_tm_legth, ''), '年',
                COALESCE(A.frtpd_tm_legth_2, ''), '月',
                COALESCE(A.frtpd_tm_legth_3, ''), '日'
            )
        WHEN A.frtpd_tp_cd_dsc = '以合同签订为准' THEN NULL
        WHEN A.frtpd_tp_cd_dsc = '无' THEN '0'
        ELSE NULL
    END AS free_prd_ent1,                                 -- 免租期_录入值1
    -- 免租期_录入值2：如果免租期是区间，则取"最多"值；其他情况为空值
    CASE
        WHEN A.frtpd_tp_cd_dsc = '区间' THEN
            CONCAT(
                COALESCE(A.frtpd_to, ''), '年',
                COALESCE(A.frtpd_to_2, ''), '月',
                COALESCE(A.frtpd_to_3, ''), '日'
            )
        WHEN A.frtpd_tp_cd_dsc IN ('固定值', '以合同签订为准', '无') THEN NULL
        ELSE NULL
    END AS free_prd_ent2,                                 -- 免租期_录入值2
    A.free_prd_std_d,                              -- 免租期_标准化（天）

    -- 成交相关字段
    CASE
        WHEN LENGTH(E.deal_date) = 8 THEN CONCAT(SUBSTR(E.deal_date, 1, 4), '-', SUBSTR(E.deal_date, 5, 2), '-', SUBSTR(E.deal_date, 7, 2))
        WHEN LENGTH(E.deal_date) = 6 THEN CONCAT(SUBSTR(E.deal_date, 1, 4), '-', SUBSTR(E.deal_date, 5, 2))
        ELSE E.deal_date
    END AS deal_date,                      -- 成交日期
    E.sevly_valut_cd_dsc AS deal_price_type,                            -- 成交计价方式
    E.deal_price AS deal_price_uni,                                  -- 成交租金价格_统一计价录入值
    E.deal_rent_unit  AS deal_price_unit_uni,                -- 成交租金价格单位_统一计价录入值
    E.deal_rent_prc_rmrk AS deal_price_remark,                          -- 成交租金价格备注
    Q.deal_price_std_sq_d AS deal_price_std_sq_d,                       -- 成交租金价格_标准化（元/平方米/天）
    E.deal_total_price AS deal_total_price,                             -- 成交租金总价_录入值（元）
    E.lease_area AS deal_lease_area,                                    -- 出租面积_成交录入（平方米）
    E.lease_tm AS deal_lease_prd_ent,                            -- 租赁时长_成交录入
    E.deal_lease_prd_std_d AS deal_lease_prd_std_d,                          -- 租赁时长_成交标准化（天）
    Q.deal_price_std_sq_d - A1.list_price_std_sq_d AS premium_vs_list_price,      -- 溢价金额_对比挂牌单价（元/平方米/天）
    E.deal_total_price - (A1.list_price_std_sq_d * (A.lease_prd_std_d - A.free_prd_std_d)  * A.plan_lease_area) AS premium_vs_list_total_price,       -- 溢价金额_对比挂牌总价（元）
    CASE
        WHEN A1.list_price_std_sq_d > 0 THEN (Q.deal_price_std_sq_d - A1.list_price_std_sq_d) / A1.list_price_std_sq_d
        ELSE NULL
    END AS premium_rate_vs_list_price,             -- 溢价率_对比挂牌单价
    Q.deal_price_std_sq_d - A1.appr_price_std_sq_d AS premium_vs_appr_price,       -- 溢价金额_对比评估单价（元/平方米/天）
    E.deal_total_price - (A1.appr_price_std_sq_d * (A.lease_prd_std_d - A.free_prd_std_d) * A.plan_lease_area) AS premium_vs_appr_total_price, -- 溢价金额_对比评估总价（元）
    CASE
        WHEN A1.appr_price_std_sq_d > 0 THEN (Q.deal_price_std_sq_d - A1.appr_price_std_sq_d) / A1.appr_price_std_sq_d
        ELSE NULL
    END AS premium_rate_vs_appr_price,             -- 溢价率_对比评估单价
    E.orgn_aavg_rent_prc / E.lease_area AS deal_price_precontract,                        -- 上份合同的成交租金价格（元/平方米/天）
    Q.deal_price_std_sq_d - (E.orgn_aavg_rent_prc / E.lease_area) AS increase_vs_precontract, -- 较上份合同增值（元/平方米/天）
    CASE
        WHEN (E.orgn_aavg_rent_prc / E.lease_area) > 0 THEN (Q.deal_price_std_sq_d - (E.orgn_aavg_rent_prc / E.lease_area)) / (E.orgn_aavg_rent_prc / E.lease_area)
        ELSE NULL
    END AS increase_rate_vs_precontract,           -- 较上份合同增值率
    v.act_exchange_type AS actual_deal_methond,                           -- 实际交易方式
    A.ntw_bid_tp_dsc AS bid_type,                                    -- 网络竞价类型
    CASE
        WHEN E.cntr_sign_dt IS NOT NULL THEN DATE_FORMAT(E.cntr_sign_dt, 'yyyy-MM-dd')
        ELSE E.cntr_sign_dt
    END AS sign_date_contract,                            -- 合同签订日期
    CASE
        WHEN E.cntr_eff_dt IS NOT NULL THEN DATE_FORMAT(E.cntr_eff_dt, 'yyyy-MM-dd')
        ELSE E.cntr_eff_dt
    END AS effective_date_contract,                       -- 合同生效日期

    -- 出租方相关字段
    A.lessor_nm AS lessor_name,                                   -- 出租方名称
    S.prov AS lessor_province,                               -- 出租方所在省
    S.city AS lessor_city,                                   -- 出租方所在市
    S.region_county AS lessor_district,                               -- 出租方所在区
    S.rgst_addr AS lessor_reg_addr,                               -- 出租方注册地
    S.lessor_tp_dsc AS lessor_type_sys,                                   -- 出租方类型_系统值
    S.lessor_type_research AS lessor_type_research,                       -- 出租方类型_研究中心
    S.lessor_type_municipal AS lessor_type_municipal,                     -- 出租方类型_市属
    S.ast_src_2_cla AS asset_source_l2,                               -- 资产来源(2级)
    S.ast_src_3_cla AS asset_source_l3,                               -- 资产来源(3级)
    S.ast_src_4_cla AS asset_source_l4,                               -- 资产来源(4级)
    S.ast_src_5_cla AS asset_source_l5,                               -- 资产来源(5级)
    S.blng_grp AS parent_group,                                  -- 所属集团
    A.authorize_unit AS approval_unit,                                 -- 项目批准单位
    S.entp_hier_num_dsc AS enterprise_tier_num,                           -- 企业层级数量
    S.ast_src_entp_1_cla AS asset_source_ent_l1,                           -- 资产来源（企业1级）
    S.ast_src_entp_2_cla AS asset_source_ent_l2,                           -- 资产来源（企业2级）
    S.ast_src_entp_3_cla AS asset_source_ent_l3,                           -- 资产来源（企业3级）
    S.ast_src_entp_4_cla AS asset_source_ent_l4,                           -- 资产来源（企业4级）
    S.ast_src_entp_5_cla AS asset_source_ent_l5,                           -- 资产来源（企业5级）
    S.ecn_char_dsc AS lessor_eco_nature,                             -- 出租方经济性质
    S.industry_tp_dsc AS lessor_industry_type,                          -- 出租方所属行业类型
    S.industry_dsc AS lessor_industry,                               -- 出租方所属行业
    S.business_scope AS lessor_biz_scope,                              -- 出租方经营范围

    -- 承租方相关字段
    T.reg_intend_lessee_cnt AS reg_intend_lessee_cnt,                         -- 报名的意向承租方数量
    T.mm_intend_lessee_cnt AS mm_intend_lessee_cnt,                           -- 交保的意向承租方数量
    T.bid_intend_lessee_cnt AS bid_intend_lessee_cnt,                         -- 具备竞价资格的意向承租方数量
    T9.intnt_rent_nm  AS lessee_name,                                   -- 承租方名称
    T9.intnt_rent_tp_dsc AS lessee_type,                                   -- 承租方类型
    T9.prov AS lessee_province,                               -- 承租方所在省
    T9.region_county AS lessee_district,                               -- 承租方所在区
    T.lessee_name_second_bid AS lessee_name_second_bid,                        -- 竞价排名第二的意向承租方名称
    T.lessee_offer_second_bid AS lessee_offer_second_bid,                       -- 竞价排名第二的意向承租方报价（元/平方米/天）
    T.lessee_name_third_bid AS lessee_name_third_bid,                         -- 竞价排名第三的意向承租方名称
    T.lessee_offer_third_bid AS lessee_offer_third_bid,                        -- 竞价排名第三的意向承租方报价（元/平方米/天）

    -- 收入和费用相关字段
    J.total_revenue AS total_revenue,                                 -- 北交所总收入（元）
    J.total_revenue - U.lessor_memb_comm - U.lessee_memb_comm AS net_revenue,                                   -- 北交所净收入（元）
    J.lessor_service_fee AS lessor_service_fee,                            -- 出租方服务费金额（元）
    J.lessee_service_fee AS lessee_service_fee,                            -- 承租方服务费金额（元）
    U.lessor_memb_comm AS lessor_memb_comm,                              -- 出租方会员分佣金额（元）
    U.lessee_memb_comm AS lessee_memb_comm,                              -- 承租方会员分佣金额（元）
    U.intend_lessee_mmb_comm AS intend_lessee_mmb_comm,                        -- 意向承租方会员分佣金额（元）
    V.settlement_dsc AS remain_pay_method,                             -- 剩余价款结算方式
    V.is_frn_cy_setl AS is_foreign_curr_settle,                        -- 是否外币结算
    V.is_mm_handle_changed AS is_mm_handle_changed,                          -- 保证金处置方式是否有变更
    V.deposit_displ_mod_dsc AS mm_handle_method,                              -- 保证金处置方式
    V.deposit_is_tfr_txn_amt AS is_mm_convert_rent,                            -- 保证金是否转成交租金总价

    -- 项目日期相关字段
    CASE
        WHEN W.prj_entry_date IS NOT NULL THEN DATE_FORMAT(W.prj_entry_date, 'yyyy-MM-dd')
        ELSE W.prj_entry_date
    END AS prj_entry_date,                                -- 项目录入日期
    CASE
        WHEN T9.ulti_lessee_confirm_date IS NOT NULL THEN DATE_FORMAT(T9.ulti_lessee_confirm_date, 'yyyy-MM-dd')
        ELSE T9.ulti_lessee_confirm_date
    END AS ulti_lessee_confirm_date,                      -- 确认最终承租方日期
    CASE
        WHEN A.esr_strt_dt IS NOT NULL AND W.prj_entry_date IS NOT NULL
        THEN datediff(
            CASE WHEN LENGTH(A.esr_strt_dt) = 8 THEN CONCAT(SUBSTR(A.esr_strt_dt, 1, 4), '-', SUBSTR(A.esr_strt_dt, 5, 2), '-', SUBSTR(A.esr_strt_dt, 7, 2)) ELSE A.esr_strt_dt END,
            DATE_FORMAT(W.prj_entry_date, 'yyyy-MM-dd')
        )
        ELSE NULL
    END AS deal_cycle_ent_list_nd,                        -- 成交周期_录入至披露_自然日
    CASE
        WHEN T9.ulti_lessee_confirm_date IS NOT NULL AND A.esr_strt_dt IS NOT NULL
        THEN datediff(
            DATE_FORMAT(T9.ulti_lessee_confirm_date, 'yyyy-MM-dd'),
            CASE WHEN LENGTH(A.esr_strt_dt) = 8 THEN CONCAT(SUBSTR(A.esr_strt_dt, 1, 4), '-', SUBSTR(A.esr_strt_dt, 5, 2), '-', SUBSTR(A.esr_strt_dt, 7, 2)) ELSE A.esr_strt_dt END
        )
        ELSE NULL
    END AS deal_cycle_list_conf_nd,                       -- 成交周期_披露至确认最终承租方_自然日
    CASE
        WHEN E.deal_date IS NOT NULL AND T9.ulti_lessee_confirm_date IS NOT NULL
        THEN datediff(
            CASE
                WHEN LENGTH(E.deal_date) = 8 THEN CONCAT(SUBSTR(E.deal_date, 1, 4), '-', SUBSTR(E.deal_date, 5, 2), '-', SUBSTR(E.deal_date, 7, 2))
                WHEN LENGTH(E.deal_date) = 6 THEN CONCAT(SUBSTR(E.deal_date, 1, 4), '-', SUBSTR(E.deal_date, 5, 2), '-01')
                ELSE E.deal_date
            END,
            DATE_FORMAT(T9.ulti_lessee_confirm_date, 'yyyy-MM-dd')
        )
        ELSE NULL
    END AS deal_cycle_conf_deal_nd,                       -- 成交周期_确认最终承租方至成交_自然日
    CASE
        WHEN E.deal_date IS NOT NULL AND W.prj_entry_date IS NOT NULL
        THEN datediff(
            CASE
                WHEN LENGTH(E.deal_date) = 8 THEN CONCAT(SUBSTR(E.deal_date, 1, 4), '-', SUBSTR(E.deal_date, 5, 2), '-', SUBSTR(E.deal_date, 7, 2))
                WHEN LENGTH(E.deal_date) = 6 THEN CONCAT(SUBSTR(E.deal_date, 1, 4), '-', SUBSTR(E.deal_date, 5, 2), '-01')
                ELSE E.deal_date
            END,
            DATE_FORMAT(W.prj_entry_date, 'yyyy-MM-dd')
        )
        ELSE NULL
    END AS deal_cycle_ent_deal_nd,                        -- 成交周期_录入至成交_自然日
    CASE
        WHEN E.deal_date IS NOT NULL AND A.esr_strt_dt IS NOT NULL
        THEN datediff(
            CASE
                WHEN LENGTH(E.deal_date) = 8 THEN CONCAT(SUBSTR(E.deal_date, 1, 4), '-', SUBSTR(E.deal_date, 5, 2), '-', SUBSTR(E.deal_date, 7, 2))
                WHEN LENGTH(E.deal_date) = 6 THEN CONCAT(SUBSTR(E.deal_date, 1, 4), '-', SUBSTR(E.deal_date, 5, 2), '-01')
                ELSE E.deal_date
            END,
            CASE WHEN LENGTH(A.esr_strt_dt) = 8 THEN CONCAT(SUBSTR(A.esr_strt_dt, 1, 4), '-', SUBSTR(A.esr_strt_dt, 5, 2), '-', SUBSTR(A.esr_strt_dt, 7, 2)) ELSE A.esr_strt_dt END
        )
        ELSE NULL
    END AS deal_cycle_list_deal_nd,                       -- 成交周期_披露至成交_自然日

    -- 成交周期_工作日
    WD1.ent_list_wd AS deal_cycle_ent_list_wd,                        -- 成交周期_录入至披露_工作日
    WD2.list_conf_wd AS deal_cycle_list_conf_wd,                       -- 成交周期_披露至确认最终承租方_工作日
    WD3.conf_deal_wd AS deal_cycle_conf_deal_wd,                       -- 成交周期_确认最终承租方至成交_工作日
    WD4.ent_deal_wd AS deal_cycle_ent_deal_wd,                        -- 成交周期_录入至成交_工作日
    WD5.list_deal_wd AS deal_cycle_list_deal_wd,                       -- 成交周期_披露至成交_工作日

    -- 其他字段
    vg.WGCS AS prj_view_cnt,                                                -- 项目围观次数
    X.prj_follow_cnt AS prj_follow_cnt,                                -- 项目关注用户数
    B.star_rnt_tp_dsc AS start_lease_type, --起租类型
    B.star_rnt_dt AS start_lease_date,  --起租日
    CASE
        WHEN B.rnt_fee_adj_mth_dsc = '2' THEN '有'
        WHEN B.rnt_fee_adj_mth_dsc = '1' THEN '无'
        WHEN B.rnt_fee_adj_mth_dsc = '3' THEN '其他'
        ELSE B.rnt_fee_adj_mth_dsc
    END AS price_adjust_method, -- 租金调整方式
    B.rnt_fee_prj_prc_rmrk AS list_price_notes, -- 租金挂牌价补充说明
    B.rnt_fee_sich_pay_quest AS rent_mm_pay_require, -- 租金及押金支付要求
    B.is_rent_cover_other_fees AS is_rent_cover_other_fees, -- 租金挂牌价是否含其他费用
    B.rent_bear_cost AS lessee_resp_fees, -- 承租方需承担费用
    B.not_coll_intnt_rent_opt AS no_intend_lessee_opt, -- 未征集到意向承租方选项
    CASE
        WHEN B.is_prmt_dcrt_rfm = '0' THEN '否'
        WHEN B.is_prmt_dcrt_rfm = '1' THEN '是'
        ELSE B.is_prmt_dcrt_rfm
    END AS is_improve_allowed, -- 是否允许装修改造
    CASE
        WHEN B.is_ivl_intnt_rent_business_scope = '0' THEN '否'
        WHEN B.is_ivl_intnt_rent_business_scope = '1' THEN '是'
        ELSE B.is_ivl_intnt_rent_business_scope
    END AS is_intend_lessee_biz_inv, -- 是否涉及意向承租方经营范围 
    CASE
        WHEN B.est_is_onet_esr = '0' THEN '否'
        WHEN B.est_is_onet_esr = '1' THEN '是'
        ELSE B.est_is_onet_esr
    END AS is_appraise_disclosed, -- 估价是否外网披露
    B.othr_esr_itm AS other_list_items, -- 其他披露事项
    B.apnt_yr_dys AS annual_days_sys, -- 年天数（天）_系统值
    B.apnt_mo_dys AS monthly_days_sys -- 月天数（天）_系统值
FROM tzccz_zcczxm  A --k1
LEFT JOIN std.std_bjhl_tzccz_zcczxm_d B
ON A.project_code = B.project_code and B.dt = '${dmp_day}'
LEFT JOIN gp_gj_bzh A1 --挂牌/估价标准化
ON A.project_code = A1.project_code
LEFT JOIN tzccz_zcczxm_fw_2 C  -- K7 房屋信息
ON A.bsn_prj_wrd = C.bsn_prj_wrd
LEFT JOIN tzccz_czfxx D --k2 出租方信息
on A.bsn_prj_wrd = 'BJHL' || D.project_id || 'ZCCZ'
left join tzccz_cjjl  E -- k3 成交记录
on A.bsn_prj_wrd = E.bsn_prj_wrd
left join tudi_2 F --k8 土地
on A.bsn_prj_wrd = F.bsn_prj_wrd
left join jxsb_agg G_AGG -- 机械设备聚合
on A.bsn_prj_wrd = 'BJHL' || G_AGG.project_id || 'ZCCZ'
left join jtgj_agg H_AGG --交通运输工具聚合
on A.bsn_prj_wrd = 'BJHL' || H_AGG.project_id || 'ZCCZ'
left join qt_agg I_AGG --其他资产聚合
on A.bsn_prj_wrd = 'BJHL' || I_AGG.project_id || 'ZCCZ'
left join jyfwf J -- 交易服务费
ON A.bsn_prj_wrd = J.bsn_prj_wrd
left join dim.dim_build_info L
on A.bsn_prj_wrd = L.bsn_prj_wrd
and L.dt = '${dmp_day}'
and L.edw_end_dt = '20991231'
LEFT JOIN tzccz_czfxx S
ON A.bsn_prj_wrd =  'BJHL'||S.project_id||'ZCCZ'
LEFT JOIN tzccz_yxczfxx_1 Y
ON A.bsn_prj_wrd =  'BJHL'||Y.project_id||'ZCCZ'
LEFT JOIN tbid_fwhydy Z
ON A.project_code = Z.project_code
left join pro_info2 P --项目挂牌信息(再加工)
ON A.project_code = P.project_code
left join cjjl_bzh Q --成交记录分别计价明细
ON A.project_code = Q.project_code
left join 
(
    SELECT 
        'BJHL'||prj||'ZCCZ' AS bsn_prj_wrd,
        rent, --最终承租方ID
        settlement_dsc,                             -- 剩余价款结算方式
        is_frn_cy_setl ,                        -- 是否外币结算
        is_mm_handle_changed ,                          -- 保证金处置方式是否有变更
        deposit_displ_mod_dsc,                              -- 保证金处置方式
        deposit_is_tfr_txn_amt,                            -- 保证金是否转成交租金总价
        act_exchange_type                           -- 实际交易方式
    FROM std.std_bjhl_tzccz_cjjl_d 
    WHERE dt = '${dmp_day}'
    and mdl_sts = 3
) V
ON a.bsn_prj_wrd = V.bsn_prj_wrd
left join tzccz_yxczfxx_2 T
ON A.bsn_prj_wrd =  'BJHL'||T.project_id||'ZCCZ'
left join zzczfxx T9
on V.bsn_prj_wrd =  T9.bsn_prj_wrd and V.rent = T9.rent
left join bjssr U
on A.project_code = U.project_code
left join pro_info W
ON A.project_code = W.project_code
left join (
    select count(DISTINCT usr_id) as prj_follow_cnt,'BJHL'||prj_id||'ZCCZ' as bsn_prj_wrd from std.std_bjhl_tl_myfocus_d
    where prj_bsn_tp_cd_dsc = '房屋出租' and dt = '${dmp_day}'
    group by 'BJHL'||prj_id||'ZCCZ'
) X
on A.bsn_prj_wrd = X.bsn_prj_wrd
LEFT JOIN
    (select  DISTINCT 'BJHL'||xmid||'ZCCZ' AS bsn_prj_wrd,WGCS FROM ods.ods_bjhl_tl_xmwgtj WHERE dt = '${dmp_day}') vg
ON a.bsn_prj_wrd = vg.bsn_prj_wrd
-- 工作日计算：录入至披露
LEFT JOIN (
    SELECT
        A.project_code,
        CASE
            WHEN A.esr_strt_dt IS NOT NULL AND W.prj_entry_date IS NOT NULL
            THEN COUNT(DISTINCT d.rq)
            ELSE NULL
        END AS ent_list_wd
    FROM tzccz_zcczxm A
    LEFT JOIN pro_info W ON A.project_code = W.project_code
    JOIN std.std_bjhl_tbid_jyr_d d
    ON d.rq >= regexp_replace(DATE_FORMAT(W.prj_entry_date, 'yyyyMMdd'), '-', '')
    AND d.rq < regexp_replace(A.esr_strt_dt, '-', '')
    AND d.JYRBS = 0
    AND d.dt = '${dmp_day}'
    WHERE A.esr_strt_dt IS NOT NULL AND W.prj_entry_date IS NOT NULL
    GROUP BY A.project_code, A.esr_strt_dt, W.prj_entry_date
) WD1 ON A.project_code = WD1.project_code
-- 工作日计算：披露至确认最终承租方
LEFT JOIN (
    SELECT
        A.project_code,
        CASE
            WHEN T.ulti_lessee_confirm_date IS NOT NULL AND A.esr_strt_dt IS NOT NULL
            THEN COUNT(DISTINCT d.rq)
            ELSE NULL
        END AS list_conf_wd
    FROM tzccz_zcczxm A
    LEFT JOIN zzczfxx T ON A.bsn_prj_wrd = T.bsn_prj_wrd
    LEFT JOIN pro_info W ON A.project_code = W.project_code
    JOIN std.std_bjhl_tbid_jyr_d d
    ON d.rq >= regexp_replace(A.esr_strt_dt, '-', '')
    AND d.rq < regexp_replace(DATE_FORMAT(T.ulti_lessee_confirm_date, 'yyyyMMdd'), '-', '')
    AND d.JYRBS = 0
    AND d.dt = '${dmp_day}'
    WHERE T.ulti_lessee_confirm_date IS NOT NULL AND A.esr_strt_dt IS NOT NULL
    GROUP BY A.project_code, T.ulti_lessee_confirm_date, A.esr_strt_dt
) WD2 ON A.project_code = WD2.project_code
-- 工作日计算：确认最终承租方至成交
LEFT JOIN (
    SELECT
        A.project_code,
        CASE
            WHEN E.deal_date IS NOT NULL AND T.ulti_lessee_confirm_date IS NOT NULL
            THEN COUNT(DISTINCT d.rq)
            ELSE NULL
        END AS conf_deal_wd
    FROM tzccz_zcczxm A
    LEFT JOIN pro_info W ON A.project_code = W.project_code
    LEFT JOIN tzccz_cjjl E ON A.bsn_prj_wrd = E.bsn_prj_wrd
    LEFT JOIN zzczfxx T ON A.bsn_prj_wrd = T.bsn_prj_wrd
    JOIN std.std_bjhl_tbid_jyr_d d
    ON d.rq >= regexp_replace(DATE_FORMAT(T.ulti_lessee_confirm_date, 'yyyyMMdd'), '-', '')
    AND d.rq < CASE
        WHEN LENGTH(E.deal_date) = 8 THEN E.deal_date
        WHEN LENGTH(E.deal_date) = 6 THEN CONCAT(E.deal_date, '01')
        ELSE regexp_replace(E.deal_date, '-', '')
    END
    AND d.JYRBS = 0
    AND d.dt = '${dmp_day}'
    WHERE E.deal_date IS NOT NULL AND T.ulti_lessee_confirm_date IS NOT NULL
    GROUP BY A.project_code, E.deal_date, T.ulti_lessee_confirm_date
) WD3 ON A.project_code = WD3.project_code
-- 工作日计算：录入至成交
LEFT JOIN (
    SELECT
        A.project_code,
        CASE
            WHEN E.deal_date IS NOT NULL AND W.prj_entry_date IS NOT NULL
            THEN COUNT(DISTINCT d.rq)
            ELSE NULL
        END AS ent_deal_wd
    FROM tzccz_zcczxm A
    LEFT JOIN pro_info W ON A.project_code = W.project_code
    LEFT JOIN tzccz_cjjl E ON A.bsn_prj_wrd = E.bsn_prj_wrd
    JOIN std.std_bjhl_tbid_jyr_d d
    ON d.rq >= regexp_replace(DATE_FORMAT(W.prj_entry_date, 'yyyyMMdd'), '-', '')
    AND d.rq < CASE
        WHEN LENGTH(E.deal_date) = 8 THEN E.deal_date
        WHEN LENGTH(E.deal_date) = 6 THEN CONCAT(E.deal_date, '01')
        ELSE regexp_replace(E.deal_date, '-', '')
    END
    AND d.JYRBS = 0
    AND d.dt = '${dmp_day}'
    WHERE E.deal_date IS NOT NULL AND W.prj_entry_date IS NOT NULL
    GROUP BY A.project_code, E.deal_date, W.prj_entry_date
) WD4 ON A.project_code = WD4.project_code
-- 工作日计算：披露至成交
LEFT JOIN (
    SELECT
        A.project_code,
        CASE
            WHEN E.deal_date IS NOT NULL AND A.esr_strt_dt IS NOT NULL
            THEN COUNT(DISTINCT d.rq)
            ELSE NULL
        END AS list_deal_wd
    FROM tzccz_zcczxm A
    LEFT JOIN tzccz_cjjl E ON A.bsn_prj_wrd = E.bsn_prj_wrd
    JOIN std.std_bjhl_tbid_jyr_d d
    ON d.rq >= regexp_replace(A.esr_strt_dt, '-', '')
    AND d.rq < CASE
        WHEN LENGTH(E.deal_date) = 8 THEN E.deal_date
        WHEN LENGTH(E.deal_date) = 6 THEN CONCAT(E.deal_date, '01')
        ELSE regexp_replace(E.deal_date, '-', '')
    END
    AND d.JYRBS = 0
    AND d.dt = '${dmp_day}'
    WHERE E.deal_date IS NOT NULL AND A.esr_strt_dt IS NOT NULL
    GROUP BY A.project_code, E.deal_date, A.esr_strt_dt
) WD5 ON A.project_code = WD5.project_code
where A.project_code not like 'GL0%' and A.project_code not like 'TL0%'
AND --手动过滤GL2020BJ1000143，否则子集改动太多，且逻辑太乱。
    (
        (A.project_code = 'GL2020BJ1000143' and T9.intnt_rent_nm = '深圳瑞银信信息技术有限公司' and E.lease_area = 408)
        or (A.project_code = 'GL2020BJ1000143' and T9.intnt_rent_nm = '北京瑞融信管理顾问有限公司' and E.lease_area = 48)
        or A.project_code != 'GL2020BJ1000143'
    )
),
-- MAP字段查询部分
map_fields_query as (
SELECT
    A.project_code AS project_id,                                   -- 项目编号
    C.basic_info_house AS basic_info_house,                                               -- 【房屋】房屋基本信息
    F.basic_info_land AS basic_info_land,                                               -- 【土地】土地基本信息
    K.list_price_sep as list_price_sep,                                -- 挂牌价格_分别计价录入值（map）
    O.appr_price_sep AS appr_price_sep,                                -- 租金估价_分别估价录入值
    R.deal_price_sep AS  deal_price_sep                                -- 成交租金价格_分别计价录入值
FROM tzccz_zcczxm  A --k1
LEFT JOIN tzccz_zcczxm_fw_2 C  -- K7 房屋信息
ON A.bsn_prj_wrd = C.bsn_prj_wrd
left join tudi_2 F --k8 土地
on A.bsn_prj_wrd = F.bsn_prj_wrd
left join fbjjmx_concat_group K --分别计价明细分组
ON A.project_code = K.project_code
left join tzccz_zcczxm_fbgjnr_concat_group O --分别估价明细分组
ON A.project_code = O.project_code
left join cjjl_fbjjnr_concat_group R --成交记录分别计价明细分组
ON A.bsn_prj_wrd = R.bsn_prj_wrd
where A.project_code not like 'GL0%' and A.project_code not like 'TL0%'
)
SELECT
    -- 项目基本信息
    main.project_id,                                   -- 项目编号
    main.project_name,                                 -- 项目名称
    main.asset_cate,                                   -- 资产类别
    main.project_status,                               -- 项目当前状态_系统值
    main.is_listing,                                   -- 是否披露中
    main.is_dealed,                                    -- 是否已成交
    main.prj_manager,                                  -- 项目负责人
    main.prj_department,                               -- 项目所属部门
    main.prj_department_claim,                         -- 项目认领部门
    main.lessee_member,                                -- 承租方受托交易服务会员
    main.lessor_member,                                -- 出租方受托交易服务会员
    main.is_state_lease,                               -- 是否国有房屋出租
    main.is_mandatory_entry,                           -- 是否强制进场_市属
    main.is_priority_right,                            -- 是否存在优先承租权
    main.priority_lessee_nm,                           -- 有优先承租权的原承租方名称
    main.is_operate,                                   -- 是否运营房源信息
    main.usage_house AS usage_require,                 -- 房产使用用途要求
    main.usage_state,                                  -- 房屋使用现状
    main.is_pre_list,                                  -- 是否有预披露项目
    main.margin_money,                                 -- 保证金金额（元）
    main.intend_lessee_cnt,                            -- 拟征集承租方个数
    main.pick_method,                                  -- 遴选方式（交易方式）
    main.online_bid_type,                              -- 网络竞价类型
    main.is_esign,                                     -- 是否需要电签

    -- 房屋维度统计字段
    main.location_cnt_house,                           -- 【房屋】坐落位置数量
    main.province_cnt_house,                           -- 【房屋】省份数量
    main.city_cnt_house,                               -- 【房屋】城市数量
    main.district_cnt_house,                           -- 【房屋】市辖区数量
    main.street_cnt_house,                             -- 【房屋】街道数量
    main.biz_district_cnt_house,                       -- 【房屋】商圈数量
    main.area_sum_house,                               -- 【房屋】总建筑面积（平方米）
    main.land_right_cnt_house,                         -- 【房屋】土地使用权性质数量
    main.asset_class_cnt_house,                        -- 【房屋】资产分类数量
    main.province_montage_house,                       -- 【房屋】省份_拼接
    main.city_montage_house,                           -- 【房屋】城市_拼接
    main.district_montage_house,                       -- 【房屋】市辖区_拼接
    main.street_montage_house,                         -- 【房屋】街道_拼接
    main.biz_district_montage_house,                   -- 【房屋】商圈_拼接
    main.land_right_montage_house,                     -- 【房屋】土地使用权性质_拼接
    main.asset_class_montage_house,                    -- 【房屋】资产分类_拼接

    -- 土地维度字段
    main.is_land,                                      -- 【土地】是否有土地
    main.location_cnt_land,                            -- 【土地】坐落位置数量
    main.area_sum_land,                                -- 【土地】总土地面积（平方米）

    -- 其他资产类别
    main.is_machine,                                   -- 【机械设备】是否有机械设备
    main.machine_nm,                                   -- 【机械设备】机械设备名称_拼接
    main.is_vehicle,                                   -- 【交通运输工具】是否有交通运输工具
    main.is_aircraft,                                  -- 【交通运输工具】是否有飞行器
    main.vehicle_num,                                  -- 【交通运输工具】交通运输工具数量
    main.is_other_asset,                               -- 【其他资产】是否有其他资产
    main.other_asset_nm,                               -- 【其他资产】其他资产名称_拼接

    -- 基本信息MAP结构
    map_fields.basic_info_house,                       -- 【房屋】房屋基本信息
    map_fields.basic_info_land,                        -- 【土地】土地基本信息

    -- 挂牌时间相关
    main.list_start_date,                              -- 当前挂牌开始日期
    main.list_end_date,                                -- 当前挂牌结束日期
    main.list_start_year,                              -- 当前挂牌开始年份
    main.list_end_year,                                -- 当前挂牌结束年份
    main.list_start_ym,                                -- 当前挂牌开始年月
    main.list_end_ym,                                  -- 当前挂牌结束年月

    -- 标准化天数
    main.annual_days,                                  -- 标准化年天数（天）
    main.monthly_days,                                 -- 标准化月天数（天）

    -- 价格相关
    main.is_sep_price,                                 -- 是否分别计价
    main.list_price_uni,                               -- 挂牌价格_统一计价录入值
    main.list_price_unit_uni,                          -- 挂牌价格单位_统一计价录入值
    map_fields.list_price_sep,                         -- 挂牌价格_分别计价录入值
    main.list_price_std_sq_d,                          -- 挂牌价格_标准化（元/平方米/天）
    main.list_price_std_sq_m,                          -- 挂牌价格_标准化（元/平方米/月）
    main.list_price_std_sq_y,                          -- 挂牌价格_标准化（元/平方米/年）
    main.list_price_std_d,                             -- 挂牌价格_标准化（元/天）
    main.list_price_std_m,                             -- 挂牌价格_标准化（元/月）
    main.list_price_std_y,                             -- 挂牌价格_标准化（元/年）

    -- 系统计算价格
    main.list_price_sys_m,                             -- 平均月租金_系统值（元/月）
    main.list_price_sys_y,                             -- 平均年租金_系统值（元/年）
    main.list_price_sys_d,                             -- 平均日租金_系统计算值（元/天）
    main.list_price_sys_std_sq_d,                      -- 平均日租金_系统计算值（元/平方米/天）
    main.list_price_sys_std_sq_m,                      -- 平均月租金_系统计算值（元/平方米/月）
    main.list_price_sys_std_sq_y,                      -- 平均年租金_系统计算值（元/平方米/年）

    -- 租赁相关
    main.lease_area,                                   -- 拟出租面积（平方米）
    main.lease_prd_type,                               -- 租赁期类型
    main.lease_prd_range_type,                         -- 租赁期区间类型
    main.lease_prd_ent1,                               -- 挂牌租赁期_录入值1
    main.lease_prd_ent2,                               -- 挂牌租赁期_录入值2
    main.lease_prd_end_date,                           -- 挂牌租赁期_截止日期
    main.lease_prd_std_d,                              -- 挂牌租赁期_标准化（天）
    main.lease_prd_std_m,                              -- 挂牌租赁期_标准化（月）
    main.lease_prd_std_y,                              -- 挂牌租赁期_标准化（年）

    -- 总价
    main.list_total_price_cal,                         -- 挂牌总价_计算值(元)
    main.list_total_price_sys,                         -- 挂牌总价_系统计算值(元)

    -- 估价相关
    main.is_sep_appraise,                              -- 是否分别估价
    main.appr_price_uni,                               -- 租金估价_统一估价录入值
    main.appr_price_unit_uni,                          -- 估价单位_统一估价录入值
    map_fields.appr_price_sep,                         -- 租金估价_分别估价录入值
    main.appr_price_std_sq_d,                          -- 租金估价_标准化（元/平方米/天）
    main.appr_total_price,                             -- 租金估价_总价(元)

    -- 披露相关
    main.re_list_num,                                  -- 重新披露次序
    main.is_last_list,                                 -- 是否最后一次披露
    main.f_list_start_date,                            -- 初次挂牌开始日期
    main.f_list_end_date,                              -- 初次挂牌结束日期
    main.f_list_start_year,                            -- 初次挂牌开始年份
    main.f_list_end_year,                              -- 初次挂牌结束年份
    main.f_list_start_ym,                              -- 初次挂牌开始年月
    main.f_list_end_ym,                                -- 初次挂牌结束年月
    main.f_list_price_std_sq_d,                        -- 初次挂牌价格_标准化（元/平方米/天）
    main.reduce_price_std_sq_d,                        -- 降价金额_标准化（元/平方米/天）
    main.is_extend,                                    -- 是否延牌

    -- 免租期
    main.free_prd_type,                                -- 免租期类型
    main.free_prd_ent1,                                -- 免租期_录入值1
    main.free_prd_ent2,                                -- 免租期_录入值2
    main.free_prd_std_d,                               -- 免租期_标准化（天）

    -- 成交相关
    main.deal_date,                                    -- 成交日期
    main.deal_price_type,                              -- 成交计价方式
    main.deal_price_uni,                               -- 成交租金价格_统一计价录入值
    main.deal_price_unit_uni,                          -- 成交租金价格单位_统一计价录入值
    main.deal_price_remark,                            -- 成交租金价格备注
    map_fields.deal_price_sep,                         -- 成交租金价格_分别计价录入值
    main.deal_price_std_sq_d,                          -- 成交租金价格_标准化（元/平方米/天）
    main.deal_total_price,                             -- 成交租金总价_录入值（元）
    main.deal_lease_area,                              -- 出租面积_成交录入（平方米）
    main.deal_lease_prd_ent,                           -- 租赁时长_成交录入
    main.deal_lease_prd_std_d,                         -- 租赁时长_成交标准化（天）

    -- 溢价相关
    main.premium_vs_list_price,                        -- 溢价金额_对比挂牌单价（元/平方米/天）
    main.premium_vs_list_total_price,                  -- 溢价金额_对比挂牌总价（元）
    main.premium_rate_vs_list_price,                   -- 溢价率_对比挂牌单价
    main.premium_vs_appr_price,                        -- 溢价金额_对比评估单价（元/平方米/天）
    main.premium_vs_appr_total_price,                  -- 溢价金额_对比评估总价（元）
    main.premium_rate_vs_appr_price,                   -- 溢价率_对比评估单价
    main.deal_price_precontract,                       -- 上份合同的成交租金价格（元/平方米/天）
    main.increase_vs_precontract,                      -- 较上份合同增值（元/平方米/天）
    main.increase_rate_vs_precontract,                 -- 较上份合同增值率
    -- 交易方式和合同
    main.actual_deal_methond,                          -- 实际交易方式（成交方式）
    main.bid_type,                                     -- 网络竞价类型
    main.sign_date_contract,                           -- 合同签订日期
    main.effective_date_contract,                      -- 合同生效日期

    -- 出租方信息
    main.lessor_name,                                  -- 出租方名称
    main.lessor_province,                              -- 出租方所在省
    main.lessor_city,                                  -- 出租方所在市
    main.lessor_district,                              -- 出租方所在区
    main.lessor_reg_addr,                              -- 出租方注册地
    main.lessor_type_sys,                              -- 出租方类型_系统值
    main.lessor_type_research,                         -- 出租方类型_研究中心
    main.lessor_type_municipal,                        -- 出租方类型_市属
    main.asset_source_l2,                              -- 资产来源(2级)
    main.asset_source_l3,                              -- 资产来源(3级)
    main.asset_source_l4,                              -- 资产来源(4级)
    main.asset_source_l5,                              -- 资产来源(5级)
    main.parent_group,                                 -- 所属集团
    main.approval_unit,                                -- 项目批准单位
    main.enterprise_tier_num,                          -- 企业层级数量
    main.asset_source_ent_l1,                          -- 资产来源（企业1级）
    main.asset_source_ent_l2,                          -- 资产来源（企业2级）
    main.asset_source_ent_l3,                          -- 资产来源（企业3级）
    main.asset_source_ent_l4,                          -- 资产来源（企业4级）
    main.asset_source_ent_l5,                          -- 资产来源（企业5级）
    main.lessor_eco_nature,                            -- 出租方经济性质
    main.lessor_industry_type,                         -- 出租方所属行业类型
    main.lessor_industry,                              -- 出租方所属行业
    main.lessor_biz_scope,                             -- 出租方经营范围

    -- 意向承租方统计
    main.reg_intend_lessee_cnt,                        -- 报名的意向承租方数量
    main.mm_intend_lessee_cnt,                         -- 交保的意向承租方数量
    main.bid_intend_lessee_cnt,                        -- 具备竞价资格的意向承租方数量

    -- 承租方信息
    main.lessee_name,                                  -- 承租方名称
    main.lessee_type,                                  -- 承租方类型
    main.lessee_province,                              -- 承租方所在省
    main.lessee_district,                              -- 承租方所在区
    main.lessee_name_second_bid,                       -- 竞价排名第二的意向承租方名称
    main.lessee_offer_second_bid,                      -- 竞价排名第二的意向承租方报价（元/平方米/天）
    main.lessee_name_third_bid,                        -- 竞价排名第三的意向承租方名称
    main.lessee_offer_third_bid,                       -- 竞价排名第三的意向承租方报价（元/平方米/天）
    -- 北交所收入和费用
    main.total_revenue,                                -- 北交所总收入（元）
    main.net_revenue,                                  -- 北交所净收入（元）
    main.lessor_service_fee,                           -- 出租方服务费金额（元）
    main.lessee_service_fee,                           -- 承租方服务费金额（元）
    main.lessor_memb_comm,                             -- 出租方会员分佣金额（元）
    main.lessee_memb_comm,                             -- 承租方会员分佣金额（元）
    main.intend_lessee_mmb_comm,                       -- 意向承租方会员分佣金额（元）

    -- 结算相关
    main.remain_pay_method,                            -- 剩余价款结算方式
    main.is_foreign_curr_settle,                       -- 是否外币结算
    main.is_mm_handle_changed,                         -- 保证金处置方式是否有变更
    main.mm_handle_method,                             -- 保证金处置方式
    main.is_mm_convert_rent,                           -- 保证金是否转成交租金总价
    -- 时间节点
    main.prj_entry_date,                               -- 项目录入日期
    main.ulti_lessee_confirm_date,                     -- 确认最终承租方日期

    -- 成交周期_自然日
    main.deal_cycle_ent_list_nd,                       -- 成交周期_录入至披露_自然日
    main.deal_cycle_list_conf_nd,                      -- 成交周期_披露至确认最终承租方_自然日
    main.deal_cycle_conf_deal_nd,                      -- 成交周期_确认最终承租方至成交_自然日
    main.deal_cycle_ent_deal_nd,                       -- 成交周期_录入至成交_自然日
    main.deal_cycle_list_deal_nd,                      -- 成交周期_披露至成交_自然日

    -- 成交周期_工作日
    main.deal_cycle_ent_list_wd,                       -- 成交周期_录入至披露_工作日
    main.deal_cycle_list_conf_wd,                      -- 成交周期_披露至确认最终承租方_工作日
    main.deal_cycle_conf_deal_wd,                      -- 成交周期_确认最终承租方至成交_工作日
    main.deal_cycle_ent_deal_wd,                       -- 成交周期_录入至成交_工作日
    main.deal_cycle_list_deal_wd,                      -- 成交周期_披露至成交_工作日

    -- 项目关注度
    main.prj_view_cnt,                                 -- 项目围观次数
    main.prj_follow_cnt,                               -- 项目关注用户数

    -- 补充字段
    main.price_adjust_method,                          -- 租金调整方式
    main.list_price_notes,                             -- 租金挂牌价补充说明
    main.rent_mm_pay_require,                          -- 租金及押金支付要求
    main.is_rent_cover_other_fees,                     -- 租金挂牌价是否含其他费用
    main.lessee_resp_fees,                             -- 承租方需承担费用
    main.no_intend_lessee_opt,                         -- 未征集到意向承租方选项
    main.is_improve_allowed,                           -- 是否允许装修改造
    main.is_intend_lessee_biz_inv,                     -- 是否涉及意向承租方经营范围
    main.is_appraise_disclosed,                        -- 估价是否外网披露
    main.other_list_items,                             -- 其他披露事项
    main.annual_days_sys,                              -- 年天数（天）_系统值
    main.monthly_days_sys                              -- 月天数（天）_系统值
FROM main_query_no_map main
LEFT JOIN map_fields_query map_fields
ON main.project_id = map_fields.project_id
